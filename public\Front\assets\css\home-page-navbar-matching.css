/* ========================================
   HOME PAGE STYLES MATCHING NAVBAR DESIGN
   Ensures consistent styling across all home page elements
   ======================================== */

/* ========================================
   GLOBAL CONSISTENCY VARIABLES
   ======================================== */
:root {
    /* Navbar matching colors */
    --navbar-primary: #667eea;
    --navbar-secondary: #764ba2;
    --navbar-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --navbar-shadow: 0 2px 15px rgba(102, 126, 234, 0.2);
    --navbar-hover-shadow: 0 4px 25px rgba(102, 126, 234, 0.3);
}

/* ========================================
   HERO SECTION ENHANCEMENTS
   ======================================== */
.banner_section {
    background: var(--navbar-gradient) !important;
    position: relative !important;
    overflow: hidden !important;
}

.banner_section::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%) !important;
    z-index: 1 !important;
}

.banner_section .container {
    position: relative !important;
    z-index: 2 !important;
}

.slider_block {
    background: transparent !important;
    border: none !important;
}

.slider_text h1 {
    color: #ffffff !important;
    text-shadow: 0 2px 10px rgba(0,0,0,0.3) !important;
    background: linear-gradient(135deg, #ffffff 0%, rgba(255,255,255,0.9) 100%) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
}

.slider_text p {
    color: rgba(255,255,255,0.95) !important;
    text-shadow: 0 1px 5px rgba(0,0,0,0.2) !important;
}

/* ========================================
   BUTTONS MATCHING NAVBAR STYLE
   ======================================== */
.default-btn, .btn-primary, .main-btn {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
    border: 2px solid rgba(255,255,255,0.2) !important;
    color: #ffffff !important;
    padding: 12px 30px !important;
    border-radius: 25px !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3) !important;
    text-decoration: none !important;
}

.default-btn:hover, .btn-primary:hover, .main-btn:hover {
    background: linear-gradient(135deg, #42a5f5 0%, #00e5ff 100%) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(79, 172, 254, 0.5) !important;
    border-color: rgba(255,255,255,0.4) !important;
    color: #ffffff !important;
    text-decoration: none !important;
}

/* ========================================
   PRODUCT CARDS MATCHING NAVBAR
   ======================================== */
.products-box, .single-arrivals-products, .single-bestsellers-products,
.single-special-products, .single-offer-products, .single-hot-products {
    border: 1px solid rgba(102, 126, 234, 0.1) !important;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.08) !important;
    transition: all 0.3s ease !important;
}

.products-box:hover, .single-arrivals-products:hover, .single-bestsellers-products:hover,
.single-special-products:hover, .single-offer-products:hover, .single-hot-products:hover {
    box-shadow: 0 8px 30px rgba(102, 126, 234, 0.15) !important;
    border-color: rgba(102, 126, 234, 0.2) !important;
}

.products-content .price, .price {
    color: var(--navbar-primary) !important;
    font-weight: 700 !important;
}

/* ========================================
   SECTION BACKGROUNDS
   ======================================== */
.overview-area, .arrivals-products-area, .bestsellers-area,
.special-products-area, .offer-products-area, .hot-products-area {
    position: relative !important;
}

.overview-area::before, .arrivals-products-area::before, .bestsellers-area::before,
.special-products-area::before, .offer-products-area::before, .hot-products-area::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.02) 0%, rgba(118, 75, 162, 0.02) 100%) !important;
    z-index: 0 !important;
}

.overview-area .container, .arrivals-products-area .container, .bestsellers-area .container,
.special-products-area .container, .offer-products-area .container, .hot-products-area .container {
    position: relative !important;
    z-index: 1 !important;
}

/* ========================================
   ICONS AND GRAPHICS
   ======================================== */
.single-overview i, .single-featured i, .overview-box i {
    color: var(--navbar-primary) !important;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%) !important;
    border-radius: 50% !important;
    padding: 15px !important;
    font-size: 24px !important;
    transition: all 0.3s ease !important;
}

.single-overview:hover i, .single-featured:hover i, .overview-box:hover i {
    background: var(--navbar-gradient) !important;
    color: #ffffff !important;
    transform: scale(1.1) !important;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3) !important;
}

/* ========================================
   TESTIMONIALS AND REVIEWS
   ======================================== */
.single-testimonial {
    border-left: 4px solid var(--navbar-primary) !important;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%) !important;
}

.testimonial-rating i {
    color: #ffa726 !important;
}

/* ========================================
   BLOG SECTION
   ======================================== */
.single-blog {
    border-top: 4px solid var(--navbar-primary) !important;
}

.single-blog .blog-meta a {
    color: var(--navbar-primary) !important;
}

.single-blog .blog-meta a:hover {
    color: var(--navbar-secondary) !important;
}

/* ========================================
   FOOTER MATCHING
   ======================================== */
.footer-area {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%) !important;
    position: relative !important;
}

.footer-area::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    height: 4px !important;
    background: var(--navbar-gradient) !important;
    z-index: 1 !important;
}

/* ========================================
   RESPONSIVE ADJUSTMENTS
   ======================================== */
@media (max-width: 991px) {
    .banner_section {
        padding-top: 100px !important;
    }
    
    .slider_text h1 {
        font-size: 2.5rem !important;
    }
    
    .default-btn, .btn-primary, .main-btn {
        padding: 10px 25px !important;
        font-size: 14px !important;
    }
}

@media (max-width: 767px) {
    .banner_section {
        padding-top: 80px !important;
    }
    
    .slider_text h1 {
        font-size: 2rem !important;
    }
    
    .default-btn, .btn-primary, .main-btn {
        padding: 8px 20px !important;
        font-size: 13px !important;
    }
}

@media (max-width: 480px) {
    .banner_section {
        padding-top: 70px !important;
    }
    
    .slider_text h1 {
        font-size: 1.75rem !important;
    }
}

/* ========================================
   ANIMATION CONSISTENCY
   ======================================== */
.fade-in-up {
    animation: fadeInUp 0.8s ease-out !important;
}

.scale-on-hover {
    transition: transform 0.3s ease !important;
}

.scale-on-hover:hover {
    transform: scale(1.05) !important;
}

/* ========================================
   LOADING STATES
   ======================================== */
.loading-overlay {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 100%) !important;
}

.loading-spinner {
    border-top-color: #ffffff !important;
}
