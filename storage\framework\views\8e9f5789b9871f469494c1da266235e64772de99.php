<?php
use App\Models\CompanyData;
use App\Models\Countris;
use App\Models\Coins;
$Comp=CompanyData::orderBy('id','desc')->first();
$Countris=Countris::all();
use App\Models\ContactUS;
use App\Models\ItemsGroups;
$Contact=ContactUS::orderBy('id','desc')->first();
$Groups=ItemsGroups::whereIn('Store_Show',[1,3])->where('Parent',0)->get();

    if(empty(session()->get('ChangeCountryy'))) {

         $Coin=Coins::where('Draw',1)->first();
    $Country=Countris::where('Coin',$Coin->id)->first();
    session()->put('ChangeCountryy',$Country->id);
      }


use App\Models\MainEComDesign;
$main=MainEComDesign::orderBy('id','desc')->first();

?>





    <body style="background: none !important; background-color: transparent !important; background-image: none !important;">
        <!-- Start Preloader Area -->
        <div class="preloader">
            <div class="loader">
                <div class="sbl-half-circle-spin">
                    <div></div>
                </div>
            </div>
        </div>
        <!-- End Preloader Area -->

        <!-- Start Modern Top Header Area -->
        <div class="modern-top-header">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-lg-4 col-md-6">
                        <div class="top-header-info">
                            <i class="bx bx-map modern-icon"></i>
                            <span class="info-text"><?php echo e(app()->getLocale() == 'ar' ?$Comp->Address :$Comp->AddressEn); ?></span>
                        </div>
                    </div>
                    <div class="col-lg-8 col-md-6">
                        <div class="top-header-actions">
                            <div class="header-select-group">
                                <form action="<?php echo e(url('ChangeLang')); ?>" method="get" class="modern-select-form">
                                    <div class="modern-select-wrapper">
                                        <i class="bx bx-globe"></i>
                                        <select name="lang" id="lang" onchange="this.form.submit()" class="modern-select">
                                            <option value="en" <?php if(app()->getLocale() == 'en'): ?> selected <?php endif; ?>>English</option>
                                            <option value="ar" <?php if(app()->getLocale() == 'ar'): ?> selected <?php endif; ?>>العربيّة</option>
                                        </select>
                                    </div>
                                </form>

                                <form action="<?php echo e(url('ChangeCountrySession')); ?>" method="get" class="modern-select-form">
                                    <div class="modern-select-wrapper">
                                        <i class="bx bx-world"></i>
                                        <select name="Country" id="coun" onchange="this.form.submit()" class="modern-select">
                                            <?php $__currentLoopData = $Countris; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $count): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($count->id); ?>" <?php if(session()->get('ChangeCountryy') == $count->id): ?> selected <?php endif; ?>><?php echo e(app()->getLocale() == 'ar' ?$count->Arabic_Name :$count->English_Name); ?></option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                    </div>
                                </form>
                            </div>

                            <div class="auth-actions">
                                <?php if(empty(auth()->guard('client')->user()->id)): ?>
                                    <a href="<?php echo e(url('LoginSite')); ?>" class="auth-link">
                                        <i class="bx bx-log-in"></i>
                                        <span><?php echo e(trans('admin.Login')); ?></span>
                                    </a>
                                    <a href="<?php echo e(url('RegisterSite')); ?>" class="auth-link register-btn">
                                        <i class="bx bx-user-plus"></i>
                                        <span><?php echo e(trans('admin.Register')); ?></span>
                                    </a>
                                <?php else: ?>
                                    <a href="<?php echo e(url('MyAccountSite')); ?>" class="auth-link">
                                        <i class="bx bx-user-circle"></i>
                                        <span><?php echo e(trans('admin.MyAccount')); ?></span>
                                    </a>
                                    <a href="<?php echo e(url('LogoutSite')); ?>" class="auth-link logout-btn">
                                        <i class="bx bx-log-out"></i>
                                        <span><?php echo e(trans('admin.Logout')); ?></span>
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- End Modern Top Header Area -->

        <!-- Start Modern Middle Header Area -->
        <div class="modern-middle-header">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-lg-3 col-md-6">
                        <div class="modern-logo-section">
                            <a href="<?php echo e(url('/')); ?>" class="modern-logo-link">
                                <div class="logo-container">
                                    <img src="<?php echo e(URL::to($Comp->Logo_Store)); ?>" alt="logo" class="modern-company-logo">
                                    <div class="company-info">
                                        <div class="company-name-modern"><?php echo e(app()->getLocale() == 'ar' ? $Comp->Name : $Comp->NameEn); ?></div>
                                        <div class="company-tagline">ERP Solutions</div>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>

                    <div class="col-lg-6 col-md-12">
                        <div class="modern-search-section">
                            <form class="modern-search-form" action="<?php echo e(url('ShopFilterName')); ?>" method="get">
                                <div class="search-input-group">
                                    <div class="search-icon">
                                        <i class="bx bx-search"></i>
                                    </div>
                                    <input type="text" name="search" class="modern-search-input" placeholder="<?php echo e(trans('admin.Search_For_Products')); ?>">
                                    <button type="submit" class="modern-search-btn">
                                        <i class="bx bx-right-arrow-alt"></i>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <div class="col-lg-3 col-md-6">
                        <div class="modern-header-actions">
                            <?php if(!empty(auth()->guard('client')->user()->id)): ?>
                                <a href="<?php echo e(url('ComparePage')); ?>" class="action-btn" data-tooltip="Compare">
                                    <i class="bx bx-git-compare"></i>
                                </a>
                                <a href="<?php echo e(url('WishlistPage')); ?>" class="action-btn" data-tooltip="Wishlist">
                                    <i class="bx bx-heart"></i>
                                </a>
                            <?php endif; ?>

                            <a href="<?php echo e(url('CartSite')); ?>" class="action-btn cart-btn" data-tooltip="Cart">
                                <i class="bx bx-shopping-bag"></i>
                                <span class="cart-count" id="CartCount"><?php echo e(Cart::content()->count()); ?></span>
                            </a>

                            <div class="cart-total">
                                <span class="total-amount"><?php echo e(Cart::total()); ?></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- End Modern Middle Header Area -->

        <!-- Start Modern Navbar Area -->
        <div class="modern-navbar-area">
            <div class="modern-responsive-nav">
                <div class="container">
                    <div class="responsive-nav-content">
                        <div class="mobile-logo">
                            <a href="<?php echo e(url('/')); ?>">
                                <img src="<?php echo e(URL::to($Comp->Logo_Store)); ?>" alt="logo" class="mobile-logo-img">
                                <span class="mobile-company-name"><?php echo e(app()->getLocale() == 'ar' ? $Comp->Name : $Comp->NameEn); ?></span>
                            </a>
                        </div>
                        <div class="mobile-menu-toggle" id="mobile-menu-toggle">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Mobile Menu Overlay -->
            <div class="mobile-menu-overlay" id="mobile-menu-overlay"></div>

            <!-- Mobile Menu Content -->
            <div class="mobile-menu-content" id="mobile-menu-content">
                <div class="mobile-menu-header">
                    <div class="mobile-logo">
                        <a href="<?php echo e(url('/')); ?>">
                            <img src="<?php echo e(URL::to($Comp->Logo_Store)); ?>" alt="logo" class="mobile-logo-img">
                            <span class="mobile-company-name"><?php echo e(app()->getLocale() == 'ar' ? $Comp->Name : $Comp->NameEn); ?></span>
                        </a>
                    </div>
                    <div class="mobile-menu-close" id="mobile-menu-close">
                        <i class="bx bx-x"></i>
                    </div>
                </div>

                <div class="mobile-menu-body">
                    <!-- Mobile Search Section -->
                    <div class="mobile-search-section">
                        <form class="mobile-search-form" action="<?php echo e(url('ShopFilterName')); ?>" method="get">
                            <div class="mobile-search-input-group">
                                <div class="mobile-search-icon">
                                    <i class="bx bx-search"></i>
                                </div>
                                <input type="text" name="search" class="mobile-search-input" placeholder="<?php echo e(trans('admin.Search_For_Products')); ?>">
                                <button type="submit" class="mobile-search-btn">
                                    <i class="bx bx-right-arrow-alt"></i>
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Mobile Cart Section -->
                    <div class="mobile-cart-section">
                        <a href="<?php echo e(url('CartSite')); ?>" class="mobile-cart-link">
                            <div class="mobile-cart-info">
                                <i class="bx bx-shopping-bag"></i>
                                <span class="mobile-cart-text"><?php echo e(trans('admin.Cart')); ?></span>
                                <span class="mobile-cart-count"><?php echo e(Cart::content()->count()); ?></span>
                            </div>
                            <div class="mobile-cart-total">
                                <span class="mobile-total-amount"><?php echo e(Cart::total()); ?></span>
                            </div>
                        </a>

                        <?php if(!empty(auth()->guard('client')->user()->id)): ?>
                            <div class="mobile-action-links">
                                <a href="<?php echo e(url('ComparePage')); ?>" class="mobile-action-link">
                                    <i class="bx bx-git-compare"></i>
                                    <span><?php echo e(trans('admin.Compare')); ?></span>
                                </a>
                                <a href="<?php echo e(url('WishlistPage')); ?>" class="mobile-action-link">
                                    <i class="bx bx-heart"></i>
                                    <span><?php echo e(trans('admin.Wishlist')); ?></span>
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Mobile Categories -->
                    <div class="mobile-categories-section">
                        <div class="mobile-categories-header">
                            <i class="bx bx-category"></i>
                            <span><?php echo e(trans('admin.Categories')); ?></span>
                            <i class="bx bx-chevron-down mobile-categories-toggle"></i>
                        </div>
                        <div class="mobile-categories-list">
                            <?php $__currentLoopData = $Groups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $group): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <a href="<?php echo e(url('FilterShopCat/'.$group->id)); ?>" class="mobile-category-item">
                                    <i class="bx bx-cube"></i>
                                    <span><?php echo e(app()->getLocale() == 'ar' ?$group->Name :$group->NameEn); ?></span>
                                </a>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>

                    <!-- Mobile Navigation Menu -->
                    <div class="mobile-nav-menu">
                        <a href="<?php echo e(url('/')); ?>" class="mobile-nav-link">
                            <i class="bx bx-home-alt"></i>
                            <span><?php echo e(trans('admin.Home')); ?></span>
                        </a>
                        <a href="<?php echo e(url('AboutSite')); ?>" class="mobile-nav-link">
                            <i class="bx bx-info-circle"></i>
                            <span><?php echo e(trans('admin.About')); ?></span>
                        </a>
                        <a href="<?php echo e(url('ShopSite')); ?>" class="mobile-nav-link">
                            <i class="bx bx-store"></i>
                            <span><?php echo e(trans('admin.Shop')); ?></span>
                        </a>
                        <a href="<?php echo e(url('BlogsSite')); ?>" class="mobile-nav-link">
                            <i class="bx bx-news"></i>
                            <span><?php echo e(trans('admin.Blogs')); ?></span>
                        </a>
                        <a href="<?php echo e(url('ContactSite')); ?>" class="mobile-nav-link">
                            <i class="bx bx-phone"></i>
                            <span><?php echo e(trans('admin.Contact')); ?></span>
                        </a>
                    </div>

                    <!-- Mobile Auth Actions -->
                    <div class="mobile-auth-section">
                        <?php if(empty(auth()->guard('client')->user()->id)): ?>
                            <a href="<?php echo e(url('LoginSite')); ?>" class="mobile-auth-link login-link">
                                <i class="bx bx-log-in"></i>
                                <span><?php echo e(trans('admin.Login')); ?></span>
                            </a>
                            <a href="<?php echo e(url('RegisterSite')); ?>" class="mobile-auth-link register-link">
                                <i class="bx bx-user-plus"></i>
                                <span><?php echo e(trans('admin.Register')); ?></span>
                            </a>
                        <?php else: ?>
                            <a href="<?php echo e(url('MyAccountSite')); ?>" class="mobile-auth-link account-link">
                                <i class="bx bx-user-circle"></i>
                                <span><?php echo e(trans('admin.MyAccount')); ?></span>
                            </a>
                            <a href="<?php echo e(url('LogoutSite')); ?>" class="mobile-auth-link logout-link">
                                <i class="bx bx-log-out"></i>
                                <span><?php echo e(trans('admin.Logout')); ?></span>
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="modern-main-navbar">
                <div class="container">
                    <nav class="modern-navbar">
                        <div class="modern-categories-menu">
                            <div class="categories-dropdown">
                                <button class="categories-btn">
                                    <i class="bx bx-category"></i>
                                    <span><?php echo e(trans('admin.Categories')); ?></span>
                                    <i class="bx bx-chevron-down"></i>
                                </button>
                                <div class="categories-dropdown-menu">
                                    <?php $__currentLoopData = $Groups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $group): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <a href="<?php echo e(url('FilterShopCat/'.$group->id)); ?>" class="category-item">
                                            <i class="bx bx-cube"></i>
                                            <span><?php echo e(app()->getLocale() == 'ar' ?$group->Name :$group->NameEn); ?></span>
                                        </a>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        </div>

                        <div class="modern-nav-menu">
                            <ul class="nav-menu-list">
                                <li class="nav-menu-item">
                                    <a href="<?php echo e(url('/')); ?>" class="nav-menu-link">
                                        <i class="bx bx-home-alt"></i>
                                        <span><?php echo e(trans('admin.Home')); ?></span>
                                    </a>
                                </li>

                                <li class="nav-menu-item">
                                    <a href="<?php echo e(url('AboutSite')); ?>" class="nav-menu-link">
                                        <i class="bx bx-info-circle"></i>
                                        <span><?php echo e(trans('admin.About')); ?></span>
                                    </a>
                                </li>

                                <li class="nav-menu-item">
                                    <a href="<?php echo e(url('ShopSite')); ?>" class="nav-menu-link">
                                        <i class="bx bx-store"></i>
                                        <span><?php echo e(trans('admin.Shop')); ?></span>
                                    </a>
                                </li>

                                <li class="nav-menu-item">
                                    <a href="<?php echo e(url('BlogsSite')); ?>" class="nav-menu-link">
                                        <i class="bx bx-news"></i>
                                        <span><?php echo e(trans('admin.Blogs')); ?></span>
                                    </a>
                                </li>

                                <li class="nav-menu-item">
                                    <a href="<?php echo e(url('ContactSite')); ?>" class="nav-menu-link">
                                        <i class="bx bx-phone"></i>
                                        <span><?php echo e(trans('admin.Contact')); ?></span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </nav>
                </div>
            </div>
        </div>
        <!-- End Modern Navbar Area -->
<style>
    /* Remove all body backgrounds */
    body {
        background: none !important;
        background-color: transparent !important;
        background-image: none !important;
    }

    /* Modern Tech/SaaS Header Styles */

    /* Preloader Styles */
    .preloader {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    }

    .preloader .loader .sbl-half-circle-spin div {
        border: 2px solid #ffffff !important;
    }

    .preloader .loader .sbl-half-circle-spin {
        border: 2px solid rgba(255,255,255,0.3) !important;
    }

    /* Modern Top Header */
    .modern-top-header {
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        padding: 8px 0;
        font-size: 14px;
        border-bottom: 1px solid rgba(255,255,255,0.1);
    }

    .top-header-info {
        display: flex;
        align-items: center;
        color: #ffffff;
    }

    .modern-icon {
        font-size: 16px;
        margin-right: 8px;
        color: #64b5f6;
    }

    .info-text {
        font-weight: 400;
        opacity: 0.9;
    }

    .top-header-actions {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 20px;
    }

    .header-select-group {
        display: flex;
        gap: 15px;
    }

    .modern-select-form {
        margin: 0;
    }

    .modern-select-wrapper {
        display: flex;
        align-items: center;
        background: rgba(255,255,255,0.1);
        border-radius: 20px;
        padding: 4px 12px;
        transition: all 0.3s ease;
    }

    .modern-select-wrapper:hover {
        background: rgba(255,255,255,0.2);
    }

    .modern-select-wrapper i {
        color: #64b5f6;
        margin-right: 6px;
        font-size: 14px;
    }

    .modern-select {
        background: transparent;
        border: none;
        color: #000;
        font-size: 13px;
        outline: none;
        cursor: pointer;
    }

    .modern-select option {
        background: #1e3c72;
        color: #000;
    }

    .auth-actions {
        display: flex;
        gap: 10px;
    }

    .auth-link {
        display: flex;
        align-items: center;
        gap: 5px;
        color: #ffffff;
        text-decoration: none;
        padding: 6px 12px;
        border-radius: 15px;
        font-size: 13px;
        font-weight: 500;
        transition: all 0.3s ease;
        background: rgba(255,255,255,0.1);
    }

    .auth-link:hover {
        background: rgba(255,255,255,0.2);
        color: #ffffff;
        text-decoration: none;
        transform: translateY(-1px);
    }

    .register-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .logout-btn {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    }

    /* Modern Middle Header */
    .modern-middle-header {
        background: #ffffff;
        padding: 20px 0;
        box-shadow: 0 2px 20px rgba(0,0,0,0.08);
        border-bottom: 1px solid #f0f0f0;
    }

    .modern-logo-section {
        display: flex;
        align-items: center;
    }

    .modern-logo-link {
        text-decoration: none;
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .logo-container {
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .modern-company-logo {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        object-fit: cover;
        border: 3px solid #667eea;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        transition: all 0.3s ease;
    }

    .modern-company-logo:hover {
        transform: scale(1.05);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    }

    .company-info {
        display: flex;
        flex-direction: column;
    }

    .company-name-modern {
        font-size: 18px;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 2px;
        line-height: 1.2;
    }

    .company-tagline {
        font-size: 12px;
        color: #667eea;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    /* Modern Search Section */
    .modern-search-section {
        display: flex;
        justify-content: center;
        margin: 10px 0;
    }

    .modern-search-form {
        width: 100%;
        max-width: 500px;
    }

    .search-input-group {
        position: relative;
        display: flex;
        align-items: center;
        background: #f8f9fa;
        border-radius: 25px;
        padding: 5px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }

    .search-input-group:focus-within {
        box-shadow: 0 4px 20px rgba(102, 126, 234, 0.2);
        background: #ffffff;
    }

    .search-icon {
        padding: 0 15px;
        color: #667eea;
        font-size: 18px;
    }

    .modern-search-input {
        flex: 1;
        border: none;
        background: transparent;
        padding: 12px 10px;
        font-size: 15px;
        color: #2c3e50;
        outline: none;
    }

    .modern-search-input::placeholder {
        color: #95a5a6;
        font-weight: 400;
    }

    .modern-search-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 20px;
        padding: 10px 20px;
        color: #ffffff;
        font-size: 16px;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 5px;
    }

    .modern-search-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    }

    /* Modern Header Actions */
    .modern-header-actions {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 15px;
    }

    .action-btn {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 45px;
        height: 45px;
        background: #f8f9fa;
        border-radius: 50%;
        color: #2c3e50;
        text-decoration: none;
        font-size: 20px;
        transition: all 0.3s ease;
        border: 2px solid transparent;
    }

    .action-btn:hover {
        background: #667eea;
        color: #ffffff;
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        text-decoration: none;
    }

    .cart-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: #ffffff;
    }

    .cart-count {
        position: absolute;
        top: -5px;
        right: -5px;
        background: #ff6b6b;
        color: #ffffff;
        border-radius: 50%;
        width: 20px;
        height: 20px;
        font-size: 11px;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 2px solid #ffffff;
    }

    .cart-total {
        background: #f8f9fa;
        padding: 8px 15px;
        border-radius: 20px;
        border: 2px solid #e9ecef;
    }

    .total-amount {
        font-weight: 600;
        color: #2c3e50;
        font-size: 14px;
    }

    /* Modern Navbar */
    .modern-navbar-area {
        background: #ffffff;
        border-top: 1px solid #f0f0f0;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    }

    .modern-main-navbar {
        padding: 0;
    }

    .modern-navbar {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 15px 0;
    }

    /* Categories Menu */
    .modern-categories-menu {
        position: relative;
    }

    .categories-btn {
        display: flex;
        align-items: center;
        gap: 10px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: #ffffff;
        border: none;
        padding: 12px 20px;
        border-radius: 25px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 14px;
    }

    .categories-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    }

    .categories-dropdown-menu {
        position: absolute;
        top: 100%;
        left: 0;
        background: #ffffff;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        padding: 15px 0;
        min-width: 250px;
        z-index: 1000;
        opacity: 0;
        visibility: hidden;
        transform: translateY(-10px);
        transition: all 0.3s ease;
        border: 1px solid #f0f0f0;
    }

    .categories-dropdown:hover .categories-dropdown-menu {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
    }

    .category-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px 20px;
        color: #2c3e50;
        text-decoration: none;
        transition: all 0.3s ease;
        font-weight: 500;
    }

    .category-item:hover {
        background: #f8f9fa;
        color: #667eea;
        text-decoration: none;
        padding-left: 25px;
    }

    .category-item i {
        font-size: 16px;
        color: #667eea;
    }

    /* Navigation Menu */
    .modern-nav-menu {
        flex: 1;
        display: flex;
        justify-content: center;
    }

    .nav-menu-list {
        display: flex;
        list-style: none;
        margin: 0;
        padding: 0;
        gap: 30px;
    }

    .nav-menu-item {
        position: relative;
    }

    .nav-menu-link {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #2c3e50;
        text-decoration: none;
        font-weight: 600;
        font-size: 15px;
        padding: 10px 15px;
        border-radius: 20px;
        transition: all 0.3s ease;
        position: relative;
    }

    .nav-menu-link:hover {
        color: #667eea;
        background: rgba(102, 126, 234, 0.1);
        text-decoration: none;
        transform: translateY(-1px);
    }

    .nav-menu-link i {
        font-size: 16px;
    }

    /* Mobile Responsive */
    .modern-responsive-nav {
        display: none;
        background: #ffffff;
        padding: 15px 0;
        border-bottom: 1px solid #f0f0f0;
    }

    .responsive-nav-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .mobile-logo {
        display: flex;
        align-items: center;
        gap: 10px;
        text-decoration: none;
    }

    .mobile-logo-img {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        object-fit: cover;
        border: 2px solid #667eea;
    }

    .mobile-company-name {
        font-weight: 600;
        color: #2c3e50;
        font-size: 16px;
    }

    .mobile-menu-toggle {
        display: flex;
        flex-direction: column;
        gap: 4px;
        cursor: pointer;
        padding: 5px;
    }

    .mobile-menu-toggle span {
        width: 25px;
        height: 3px;
        background: #667eea;
        border-radius: 2px;
        transition: all 0.3s ease;
    }

    .mobile-menu-toggle.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    .mobile-menu-toggle.active span:nth-child(2) {
        opacity: 0;
    }

    .mobile-menu-toggle.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }

    /* Mobile Menu Overlay */
    .mobile-menu-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 9998;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }

    .mobile-menu-overlay.active {
        opacity: 1;
        visibility: visible;
    }

    /* Mobile Menu Content */
    .mobile-menu-content {
        position: fixed;
        top: 0;
        right: -100%;
        width: 280px;
        height: 100%;
        background: #ffffff;
        z-index: 9999;
        transition: all 0.3s ease;
        box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
        overflow-y: auto;
    }

    .mobile-menu-content.active {
        right: 0;
    }

    .mobile-menu-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 15px;
        border-bottom: 1px solid #f0f0f0;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: #ffffff;
        min-height: 60px;
    }

    .mobile-menu-header .mobile-logo a {
        color: #ffffff;
        text-decoration: none;
    }

    .mobile-menu-header .mobile-company-name {
        color: #ffffff;
        font-size: 14px;
    }

    .mobile-menu-close {
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .mobile-menu-close:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: scale(1.05);
    }

    .mobile-menu-close i {
        font-size: 16px;
        color: #ffffff;
    }

    .mobile-menu-body {
        padding: 15px 0;
    }

    /* Mobile Categories Section */
    .mobile-categories-section {
        margin-bottom: 15px;
        border-bottom: 1px solid #f0f0f0;
        padding-bottom: 15px;
    }

    .mobile-categories-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 10px 15px;
        background: #f8f9fa;
        cursor: pointer;
        transition: all 0.3s ease;
        min-height: 40px;
    }

    .mobile-categories-header:hover {
        background: #e9ecef;
    }

    .mobile-categories-header i:first-child {
        color: #667eea;
        font-size: 16px;
    }

    .mobile-categories-header span {
        flex: 1;
        margin-left: 8px;
        font-weight: 600;
        color: #2c3e50;
        font-size: 14px;
    }

    .mobile-categories-toggle {
        color: #667eea;
        font-size: 14px;
        transition: all 0.3s ease;
    }

    .mobile-categories-header.active .mobile-categories-toggle {
        transform: rotate(180deg);
    }

    .mobile-categories-list {
        max-height: 0;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .mobile-categories-list.active {
        max-height: 250px;
        overflow-y: auto;
    }

    .mobile-category-item {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 8px 15px;
        color: #2c3e50;
        text-decoration: none;
        transition: all 0.3s ease;
        border-left: 3px solid transparent;
        font-size: 14px;
        min-height: 36px;
    }

    .mobile-category-item:hover {
        background: #f8f9fa;
        color: #667eea;
        text-decoration: none;
        border-left-color: #667eea;
        padding-left: 18px;
    }

    .mobile-category-item i {
        font-size: 14px;
        color: #667eea;
    }

    /* Mobile Navigation Menu */
    .mobile-nav-menu {
        margin-bottom: 15px;
        border-bottom: 1px solid #f0f0f0;
        padding-bottom: 15px;
    }

    .mobile-nav-link {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 10px 15px;
        color: #2c3e50;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
        border-left: 3px solid transparent;
        font-size: 14px;
        min-height: 40px;
    }

    .mobile-nav-link:hover {
        background: #f8f9fa;
        color: #667eea;
        text-decoration: none;
        border-left-color: #667eea;
        padding-left: 18px;
    }

    .mobile-nav-link i {
        font-size: 16px;
        color: #667eea;
        width: 18px;
    }

    /* Mobile Auth Section */
    .mobile-auth-section {
        padding: 0 15px;
    }

    .mobile-auth-link {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 12px;
        margin-bottom: 8px;
        color: #ffffff;
        text-decoration: none;
        border-radius: 20px;
        font-weight: 600;
        transition: all 0.3s ease;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        font-size: 13px;
        min-height: 36px;
    }

    .mobile-auth-link:hover {
        text-decoration: none;
        color: #ffffff;
        transform: translateY(-1px);
        box-shadow: 0 3px 10px rgba(102, 126, 234, 0.3);
    }

    .mobile-auth-link.register-link {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .mobile-auth-link.logout-link {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    }

    .mobile-auth-link i {
        font-size: 14px;
    }

    /* RTL Support for Mobile Menu */
    [dir="rtl"] .mobile-menu-content {
        right: auto;
        left: -100%;
    }

    [dir="rtl"] .mobile-menu-content.active {
        left: 0;
    }

    [dir="rtl"] .mobile-category-item:hover,
    [dir="rtl"] .mobile-nav-link:hover {
        border-left: none;
        border-right: 3px solid #667eea;
        padding-left: 20px;
        padding-right: 25px;
    }

    /* Mobile Search Section */
    .mobile-search-section {
        padding: 15px;
        border-bottom: 1px solid #f0f0f0;
        margin-bottom: 15px;
    }

    .mobile-search-form {
        width: 100%;
    }

    .mobile-search-input-group {
        position: relative;
        display: flex;
        align-items: center;
        background: #f8f9fa;
        border-radius: 20px;
        padding: 3px;
        transition: all 0.3s ease;
    }

    .mobile-search-input-group:focus-within {
        background: #ffffff;
        box-shadow: 0 2px 10px rgba(102, 126, 234, 0.2);
    }

    .mobile-search-icon {
        padding: 0 12px;
        color: #667eea;
        font-size: 16px;
    }

    .mobile-search-input {
        flex: 1;
        border: none;
        background: transparent;
        padding: 10px 8px;
        font-size: 14px;
        color: #2c3e50;
        outline: none;
    }

    .mobile-search-input::placeholder {
        color: #95a5a6;
        font-weight: 400;
    }

    .mobile-search-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 16px;
        padding: 8px 16px;
        color: #ffffff;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .mobile-search-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 3px 10px rgba(102, 126, 234, 0.3);
    }

    /* Mobile Cart Section */
    .mobile-cart-section {
        padding: 15px;
        border-bottom: 1px solid #f0f0f0;
        margin-bottom: 15px;
    }

    .mobile-cart-link {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 15px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        color: #ffffff;
        text-decoration: none;
        transition: all 0.3s ease;
        margin-bottom: 10px;
    }

    .mobile-cart-link:hover {
        text-decoration: none;
        color: #ffffff;
        transform: translateY(-1px);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    }

    .mobile-cart-info {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .mobile-cart-info i {
        font-size: 18px;
    }

    .mobile-cart-text {
        font-weight: 600;
        font-size: 14px;
    }

    .mobile-cart-count {
        background: rgba(255, 255, 255, 0.3);
        color: #ffffff;
        border-radius: 50%;
        width: 22px;
        height: 22px;
        font-size: 11px;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .mobile-total-amount {
        font-weight: 600;
        font-size: 14px;
    }

    .mobile-action-links {
        display: flex;
        gap: 8px;
    }

    .mobile-action-link {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        padding: 8px 12px;
        background: #f8f9fa;
        border-radius: 12px;
        color: #2c3e50;
        text-decoration: none;
        font-size: 13px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .mobile-action-link:hover {
        background: #e9ecef;
        color: #667eea;
        text-decoration: none;
    }

    .mobile-action-link i {
        font-size: 14px;
    }

    /* Responsive Design - Extended to Tablet View */
    @media (max-width: 1200px) {
        .modern-responsive-nav {
            display: block;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 9999;
            background: #ffffff;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .modern-main-navbar {
            display: none;
        }

        .modern-middle-header {
            display: none;
        }

        .modern-top-header {
            display: none !important;
        }

        .top-header-actions {
            flex-direction: column;
            gap: 10px;
        }

        .header-select-group {
            gap: 10px;
        }

        /* Add top padding to body to compensate for fixed navbar */
        body {
            padding-top: 70px;
        }

        /* Fixed navbar styling */
        .modern-responsive-nav .container {
            position: relative;
        }

        .responsive-nav-content {
            padding: 12px 0;
            transition: all 0.3s ease;
        }

        /* Navbar scroll effect */
        .modern-responsive-nav.scrolled {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }

        /* Ensure mobile menu appears above fixed navbar */
        .mobile-menu-overlay {
            top: 0;
        }

        .mobile-menu-content {
            top: 0;
        }

        /* Hide slider navigation controls in tablet and mobile */
        .home-slides.owl-theme .owl-nav,
        .home-slides.owl-theme .owl-dots,
        .home-slides-two.owl-theme .owl-nav,
        .home-slides-two.owl-theme .owl-dots,
        .banner_slider .owl-nav,
        .banner_slider .owl-dots,
        .testimonial-slides.owl-theme .owl-nav,
        .testimonial-slides.owl-theme .owl-dots,
        #banner_slider .owl-nav,
        #banner_slider .owl-dots,
        #banner_slider2 .owl-nav,
        #banner_slider2 .owl-dots,
        #testimonial_slider .owl-nav,
        #testimonial_slider .owl-dots {
            display: none !important;
        }

        /* Hide slick slider controls */
        .products-details-image-slides .slick-dots,
        .products-details-image-slides .slick-prev,
        .products-details-image-slides .slick-next,
        .slider-for .slick-dots,
        .slider-for .slick-prev,
        .slider-for .slick-next,
        .slider-nav .slick-dots,
        .slider-nav .slick-prev,
        .slider-nav .slick-next {
            display: none !important;
        }
    }

    @media (max-width: 767px) {
        .modern-top-header {
            display: none !important;
            visibility: hidden !important;
            height: 0 !important;
            padding: 0 !important;
        }

        .modern-middle-header {
            padding: 10px 0;
        }

        .modern-search-section {
            margin: 10px 0;
        }

        .search-input-group {
            padding: 2px;
        }

        .modern-search-input {
            padding: 8px 6px;
            font-size: 13px;
        }

        .modern-search-btn {
            padding: 6px 12px;
            font-size: 13px;
        }

        .action-btn {
            width: 35px;
            height: 35px;
            font-size: 16px;
        }

        .modern-header-actions {
            gap: 8px;
        }

        .top-header-info {
            font-size: 11px;
        }

        .top-header-info .info-text {
            display: none;
        }

        .auth-actions {
            flex-wrap: wrap;
            gap: 3px;
        }

        .auth-link {
            padding: 3px 6px;
            font-size: 11px;
        }

        .auth-link span {
            display: none;
        }

        .header-select-group {
            flex-direction: column;
            gap: 3px;
        }

        .modern-select-wrapper {
            padding: 2px 6px;
        }

        .modern-select {
            font-size: 11px;
        }

        /* Mobile menu adjustments for small screens */
        .mobile-menu-content {
            width: 100%;
            right: -100%;
        }

        .mobile-menu-header {
            padding: 8px 12px;
            min-height: 50px;
        }

        .mobile-menu-body {
            padding: 10px 0;
        }

        .mobile-search-section {
            padding: 10px 12px;
            margin-bottom: 10px;
        }

        .mobile-search-input {
            padding: 8px 6px;
            font-size: 13px;
        }

        .mobile-search-btn {
            padding: 6px 12px;
            font-size: 13px;
        }

        .mobile-cart-section {
            padding: 10px 12px;
            margin-bottom: 10px;
        }

        .mobile-cart-link {
            padding: 10px 12px;
        }

        .mobile-cart-text,
        .mobile-total-amount {
            font-size: 13px;
        }

        .mobile-cart-count {
            width: 20px;
            height: 20px;
            font-size: 10px;
        }

        .mobile-action-link {
            padding: 6px 8px;
            font-size: 12px;
        }

        .mobile-categories-list {
            max-height: 180px;
            overflow-y: auto;
        }

        .mobile-categories-header {
            padding: 8px 12px;
            min-height: 36px;
        }

        .mobile-nav-link,
        .mobile-category-item {
            padding: 8px 12px;
            min-height: 32px;
            font-size: 13px;
        }

        .mobile-auth-link {
            padding: 6px 10px;
            font-size: 12px;
            min-height: 32px;
            margin-bottom: 6px;
        }

        .mobile-categories-header span,
        .mobile-nav-link,
        .mobile-category-item {
            font-size: 13px;
        }

        /* Adjust body padding for smaller screens */
        body {
            padding-top: 65px;
        }

        .responsive-nav-content {
            padding: 10px 0;
        }

        /* Ensure slider controls remain hidden on smaller mobile screens */
        .home-slides.owl-theme .owl-nav,
        .home-slides.owl-theme .owl-dots,
        .banner_slider .owl-nav,
        .banner_slider .owl-dots,
        #banner_slider .owl-nav,
        #banner_slider .owl-dots,
        .owl-carousel .owl-nav,
        .owl-carousel .owl-dots {
            display: none !important;
            opacity: 0 !important;
            visibility: hidden !important;
        }
    }

    @media (max-width: 480px) {
        .modern-top-header {
            display: none !important;
            visibility: hidden !important;
            height: 0 !important;
            overflow: hidden !important;
            margin: 0 !important;
            padding: 0 !important;
        }

        .modern-middle-header {
            padding: 8px 0;
        }

        .logo-container {
            gap: 6px;
        }

        .modern-company-logo {
            width: 30px;
            height: 30px;
        }

        .company-name-modern {
            font-size: 12px;
        }

        .company-tagline {
            font-size: 9px;
        }

        .mobile-company-name {
            font-size: 12px;
        }

        .mobile-logo-img {
            width: 25px;
            height: 25px;
        }

        .search-input-group {
            padding: 1px;
        }

        .modern-search-input {
            padding: 6px 4px;
            font-size: 12px;
        }

        .modern-search-btn {
            padding: 5px 10px;
            font-size: 12px;
        }

        .action-btn {
            width: 32px;
            height: 32px;
            font-size: 14px;
        }

        .cart-count {
            width: 16px;
            height: 16px;
            font-size: 9px;
        }

        .cart-total {
            padding: 4px 8px;
        }

        .total-amount {
            font-size: 11px;
        }

        /* Ultra compact mobile menu for very small screens */
        .mobile-menu-content {
            width: 100%;
        }

        .mobile-menu-header {
            padding: 6px 10px;
            min-height: 45px;
        }

        .mobile-menu-header .mobile-company-name {
            font-size: 12px;
        }

        .mobile-menu-close {
            width: 25px;
            height: 25px;
        }

        .mobile-menu-close i {
            font-size: 14px;
        }

        .mobile-menu-body {
            padding: 8px 0;
        }

        .mobile-search-section {
            padding: 8px 10px;
            margin-bottom: 8px;
        }

        .mobile-search-input {
            padding: 6px 4px;
            font-size: 12px;
        }

        .mobile-search-btn {
            padding: 5px 10px;
            font-size: 12px;
        }

        .mobile-cart-section {
            padding: 8px 10px;
            margin-bottom: 8px;
        }

        .mobile-cart-link {
            padding: 8px 10px;
        }

        .mobile-cart-text,
        .mobile-total-amount {
            font-size: 12px;
        }

        .mobile-cart-count {
            width: 18px;
            height: 18px;
            font-size: 9px;
        }

        .mobile-action-link {
            padding: 5px 6px;
            font-size: 11px;
        }

        .mobile-action-link i {
            font-size: 12px;
        }

        .mobile-categories-header {
            padding: 6px 10px;
            min-height: 32px;
        }

        .mobile-categories-header span {
            font-size: 12px;
        }

        .mobile-categories-list {
            max-height: 150px;
        }

        .mobile-nav-link,
        .mobile-category-item {
            padding: 6px 10px;
            min-height: 28px;
            font-size: 12px;
        }

        .mobile-nav-link i,
        .mobile-category-item i {
            font-size: 13px;
        }

        .mobile-auth-section {
            padding: 0 10px;
        }

        .mobile-auth-link {
            padding: 5px 8px;
            font-size: 11px;
            min-height: 28px;
            margin-bottom: 4px;
        }

        .mobile-auth-link i {
            font-size: 12px;
        }

        /* Force hide all slider controls on very small screens */
        .owl-carousel .owl-nav,
        .owl-carousel .owl-dots,
        .slick-slider .slick-dots,
        .slick-slider .slick-prev,
        .slick-slider .slick-next,
        .home-slides .owl-nav,
        .home-slides .owl-dots,
        .banner_slider .owl-nav,
        .banner_slider .owl-dots {
            display: none !important;
            opacity: 0 !important;
            visibility: hidden !important;
            pointer-events: none !important;
        }

        /* Additional mobile spacing optimizations */
        .pt-100, .pb-100, .ptb-100 {
            padding-top: 20px !important;
            padding-bottom: 20px !important;
        }

        .pt-70, .pb-70 {
            padding-top: 15px !important;
            padding-bottom: 15px !important;
        }

        .pt-50, .pb-50 {
            padding-top: 15px !important;
            padding-bottom: 15px !important;
        }

        /* Section spacing */
        .section-title {
            margin-bottom: 15px !important;
        }

        /* Container spacing */
        .container {
            padding-left: 10px !important;
            padding-right: 10px !important;
        }

        /* Adjust body padding for very small screens */
        body {
            padding-top: 60px;
        }

        .responsive-nav-content {
            padding: 8px 0;
        }
    }

    /* Tooltip Styles */
    .action-btn[data-tooltip]:hover::before {
        content: attr(data-tooltip);
        position: absolute;
        bottom: -35px;
        left: 50%;
        transform: translateX(-50%);
        background: #2c3e50;
        color: #ffffff;
        padding: 5px 10px;
        border-radius: 5px;
        font-size: 12px;
        white-space: nowrap;
        z-index: 1000;
    }

    .action-btn[data-tooltip]:hover::after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        border: 5px solid transparent;
        border-bottom-color: #2c3e50;
        z-index: 1000;
    }

</style>
<?php /**PATH C:\xampp\htdocs\ost-erp-online\resources\views/site/layouts/navbar.blade.php ENDPATH**/ ?>