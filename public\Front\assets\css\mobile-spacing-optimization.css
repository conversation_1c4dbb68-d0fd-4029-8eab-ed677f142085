/* ========================================
   MOBILE SPACING OPTIMIZATION CSS
   Reduces spacing between sections for better mobile UX
   ======================================== */

/* ========================================
   TABLET AND MOBILE (1200px and below)
   ======================================== */
@media (max-width: 1200px) {
    /* Override default section padding */
    .pt-100 { padding-top: 50px !important; }
    .pb-100 { padding-bottom: 50px !important; }
    .ptb-100 {
        padding-top: 50px !important;
        padding-bottom: 50px !important;
    }

    .pt-70 { padding-top: 35px !important; }
    .pb-70 { padding-bottom: 35px !important; }

    .pt-50 { padding-top: 25px !important; }
    .pb-50 { padding-bottom: 25px !important; }

    /* Main content sections */
    .overview-area,
    .arrivals-products-area,
    .bestsellers-area,
    .special-products-area,
    .offer-products-area,
    .hot-products-area,
    .blog-area,
    .testimonial-area,
    .partner-area,
    .featured-area,
    .collection-area,
    .support-area,
    .fun-facts-area,
    .team-area,
    .pricing-area,
    .contact-area,
    .faq-area,
    .newsletter-area,
    .cta-area {
        padding-top: 40px !important;
        padding-bottom: 40px !important;
    }

    /* Section titles */
    .section-title {
        margin-bottom: 30px !important;
    }

    /* Restaurant view sections */
    .row_am {
        padding: 40px 0 !important;
    }

    .py_50 {
        padding-top: 25px !important;
        padding-bottom: 25px !important;
    }

    /* Card elements */
    .single-overview,
    .single-arrivals-products,
    .single-bestsellers-products,
    .single-special-products,
    .single-offer-products,
    .single-hot-products,
    .single-blog,
    .single-partner,
    .single-featured,
    .single-testimonial {
        margin-bottom: 25px !important;
        padding: 25px !important;
    }
}

/* ========================================
   TABLET SPECIFIC (992px to 1200px)
   ======================================== */
@media (min-width: 992px) and (max-width: 1200px) {
    /* Tablet-specific optimizations */
    .pt-100 { padding-top: 60px !important; }
    .pb-100 { padding-bottom: 60px !important; }
    .ptb-100 {
        padding-top: 60px !important;
        padding-bottom: 60px !important;
    }

    /* Tablet section spacing */
    .overview-area,
    .arrivals-products-area,
    .bestsellers-area,
    .special-products-area,
    .offer-products-area,
    .hot-products-area,
    .blog-area,
    .testimonial-area,
    .partner-area,
    .featured-area,
    .collection-area,
    .support-area,
    .fun-facts-area,
    .team-area,
    .pricing-area,
    .contact-area,
    .faq-area,
    .newsletter-area,
    .cta-area {
        padding-top: 50px !important;
        padding-bottom: 50px !important;
    }

    /* Tablet body padding for fixed navbar */
    body {
        padding-top: 75px !important;
    }

    .responsive-nav-content {
        padding: 15px 0 !important;
    }
}

/* ========================================
   MOBILE (767px and below)
   ======================================== */
@media (max-width: 767px) {
    /* Reduce section padding significantly */
    .pt-100 { padding-top: 30px !important; }
    .pb-100 { padding-bottom: 30px !important; }
    .ptb-100 {
        padding-top: 30px !important;
        padding-bottom: 30px !important;
    }

    .pt-70 { padding-top: 25px !important; }
    .pb-70 { padding-bottom: 25px !important; }

    .pt-50 { padding-top: 20px !important; }
    .pb-50 { padding-bottom: 20px !important; }

    /* Main content sections - more compact */
    .overview-area,
    .arrivals-products-area,
    .bestsellers-area,
    .special-products-area,
    .offer-products-area,
    .hot-products-area,
    .blog-area,
    .testimonial-area,
    .partner-area,
    .featured-area,
    .collection-area,
    .support-area,
    .fun-facts-area,
    .team-area,
    .pricing-area,
    .contact-area,
    .faq-area,
    .newsletter-area,
    .cta-area {
        padding-top: 25px !important;
        padding-bottom: 25px !important;
    }

    /* Section titles */
    .section-title {
        margin-bottom: 20px !important;
    }

    /* Restaurant view sections */
    .row_am {
        padding: 25px 0 !important;
    }

    .py_50 {
        padding-top: 20px !important;
        padding-bottom: 20px !important;
    }

    /* Card elements */
    .single-overview,
    .single-arrivals-products,
    .single-bestsellers-products,
    .single-special-products,
    .single-offer-products,
    .single-hot-products,
    .single-blog,
    .single-partner,
    .single-featured,
    .single-testimonial {
        margin-bottom: 20px !important;
        padding: 20px !important;
    }

    /* Footer spacing */
    .footer-area {
        padding-top: 40px !important;
        padding-bottom: 30px !important;
    }

    /* Page title area */
    .page-title-area {
        padding-top: 100px !important;
        padding-bottom: 100px !important;
    }

    /* Main slider */
    .main-slider-item {
        height: auto !important;
        min-height: 500px !important;
    }
}

/* ========================================
   SMALL MOBILE (480px and below)
   ======================================== */
@media (max-width: 480px) {
    /* Ultra-compact spacing */
    .pt-100 { padding-top: 20px !important; }
    .pb-100 { padding-bottom: 20px !important; }
    .ptb-100 {
        padding-top: 20px !important;
        padding-bottom: 20px !important;
    }

    .pt-70 { padding-top: 15px !important; }
    .pb-70 { padding-bottom: 15px !important; }

    .pt-50 { padding-top: 15px !important; }
    .pb-50 { padding-bottom: 15px !important; }

    /* Main content sections - ultra compact */
    .overview-area,
    .arrivals-products-area,
    .bestsellers-area,
    .special-products-area,
    .offer-products-area,
    .hot-products-area,
    .blog-area,
    .testimonial-area,
    .partner-area,
    .featured-area,
    .collection-area,
    .support-area,
    .fun-facts-area,
    .team-area,
    .pricing-area,
    .contact-area,
    .faq-area,
    .newsletter-area,
    .cta-area {
        padding-top: 20px !important;
        padding-bottom: 20px !important;
    }

    /* Section titles */
    .section-title {
        margin-bottom: 15px !important;
    }

    /* Restaurant view sections */
    .row_am {
        padding: 20px 0 !important;
    }

    .py_50 {
        padding-top: 15px !important;
        padding-bottom: 15px !important;
    }

    /* Card elements - ultra compact */
    .single-overview,
    .single-arrivals-products,
    .single-bestsellers-products,
    .single-special-products,
    .single-offer-products,
    .single-hot-products,
    .single-blog,
    .single-partner,
    .single-featured,
    .single-testimonial {
        margin-bottom: 15px !important;
        padding: 15px !important;
    }

    /* Container padding */
    .container {
        padding-left: 10px !important;
        padding-right: 10px !important;
    }

    /* Footer spacing */
    .footer-area {
        padding-top: 30px !important;
        padding-bottom: 20px !important;
    }

    /* Page title area */
    .page-title-area {
        padding-top: 80px !important;
        padding-bottom: 80px !important;
    }

    /* Main slider */
    .main-slider-item {
        height: auto !important;
        min-height: 400px !important;
    }

    /* Additional spacing optimizations */
    .banner_section {
        padding-top: 80px !important;
    }

    .banner_slider .slider_block {
        height: 400px !important;
    }
}

/* ========================================
   FIXED NAVBAR FOR TABLET AND MOBILE
   ======================================== */
@media (max-width: 1200px) {
    /* Fixed navbar positioning */
    .modern-responsive-nav {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        z-index: 9999 !important;
        background: #ffffff !important;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
        transition: all 0.3s ease !important;
    }

    /* Hide top header section completely */
    .modern-top-header {
        display: none !important;
        visibility: hidden !important;
        height: 0 !important;
        overflow: hidden !important;
    }

    /* Body padding to compensate for fixed navbar */
    body {
        padding-top: 70px !important;
    }

    /* Navbar scroll effect */
    .modern-responsive-nav.scrolled {
        background: rgba(255, 255, 255, 0.95) !important;
        backdrop-filter: blur(10px) !important;
        -webkit-backdrop-filter: blur(10px) !important;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
    }

    /* Responsive nav content padding */
    .responsive-nav-content {
        padding: 12px 0 !important;
        transition: all 0.3s ease !important;
    }
}

@media (max-width: 767px) {
    /* Adjust body padding for smaller screens */
    body {
        padding-top: 65px !important;
    }

    .responsive-nav-content {
        padding: 10px 0 !important;
    }
}

@media (max-width: 480px) {
    /* Adjust body padding for very small screens */
    body {
        padding-top: 60px !important;
    }

    .responsive-nav-content {
        padding: 8px 0 !important;
    }
}

/* ========================================
   GENERAL TABLET AND MOBILE OPTIMIZATIONS
   ======================================== */
@media (max-width: 1200px) {
    /* Remove excessive margins */
    .mb-30 { margin-bottom: 20px !important; }
    .mb-40 { margin-bottom: 25px !important; }
    .mb-50 { margin-bottom: 30px !important; }

    .mt-30 { margin-top: 20px !important; }
    .mt-40 { margin-top: 25px !important; }
    .mt-50 { margin-top: 30px !important; }

    /* Row spacing */
    .row {
        margin-left: -10px !important;
        margin-right: -10px !important;
    }

    .row > [class*="col-"] {
        padding-left: 10px !important;
        padding-right: 10px !important;
    }
}
