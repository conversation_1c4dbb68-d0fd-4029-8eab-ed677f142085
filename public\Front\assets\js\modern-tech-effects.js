/*
===========================================
Modern Tech/SaaS Interactive Effects
===========================================
This file contains modern JavaScript effects and interactions
for the tech/SaaS website design
*/

jQuery(document).ready(function($) {
    'use strict';

    // Modern Navbar Scroll Effect
    $(window).on('scroll', function() {
        var scrollTop = $(this).scrollTop();

        if (scrollTop > 100) {
            $('.modern-navbar-area').addClass('navbar-scrolled');
            $('.modern-middle-header').addClass('header-scrolled');
        } else {
            $('.modern-navbar-area').removeClass('navbar-scrolled');
            $('.modern-middle-header').removeClass('header-scrolled');
        }
    });

    // Add scroll classes to CSS
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            .navbar-scrolled {
                box-shadow: 0 4px 20px rgba(0,0,0,0.15) !important;
                backdrop-filter: blur(10px) !important;
                background: rgba(255,255,255,0.95) !important;
            }

            .header-scrolled {
                transform: translateY(-10px) !important;
                opacity: 0.95 !important;
            }
        `)
        .appendTo('head');

    // Modern Card Hover Effects
    $('.single-overview, .single-arrivals-products, .single-bestsellers-products, .single-special-products, .single-offer-products, .single-hot-products, .single-blog, .products-box').hover(
        function() {
            $(this).addClass('card-hover-effect');
        },
        function() {
            $(this).removeClass('card-hover-effect');
        }
    );

    // Add hover effect styles
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            .card-hover-effect {
                transform: translateY(-8px) scale(1.02) !important;
                box-shadow: 0 15px 40px rgba(0,0,0,0.15) !important;
                z-index: 10 !important;
                position: relative !important;
            }
        `)
        .appendTo('head');

    // Modern Button Ripple Effect
    $('.default-btn, .btn, button[type="submit"], input[type="submit"]').on('click', function(e) {
        var $this = $(this);
        var $ripple = $('<span class="ripple"></span>');
        var btnOffset = $this.offset();
        var xPos = e.pageX - btnOffset.left;
        var yPos = e.pageY - btnOffset.top;

        $ripple.css({
            top: yPos,
            left: xPos
        });

        $this.append($ripple);

        setTimeout(function() {
            $ripple.remove();
        }, 600);
    });

    // Add ripple effect styles
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            .default-btn, .btn, button[type="submit"], input[type="submit"] {
                position: relative !important;
                overflow: hidden !important;
            }

            .ripple {
                position: absolute !important;
                border-radius: 50% !important;
                background: rgba(255,255,255,0.6) !important;
                transform: scale(0) !important;
                animation: ripple-animation 0.6s linear !important;
                pointer-events: none !important;
            }

            @keyframes ripple-animation {
                to {
                    transform: scale(4) !important;
                    opacity: 0 !important;
                }
            }
        `)
        .appendTo('head');

    // Modern Smooth Scrolling
    $('a[href^="#"]').on('click', function(e) {
        e.preventDefault();
        var target = $(this.getAttribute('href'));
        if (target.length) {
            $('html, body').animate({
                scrollTop: target.offset().top - 100
            }, 800, 'easeInOutQuart');
        }
    });

    // Modern Loading Animation for Images
    $('img').each(function() {
        var $img = $(this);
        var $parent = $img.parent();

        if (!$img.hasClass('loaded')) {
            $parent.addClass('loading');

            $img.on('load', function() {
                $parent.removeClass('loading');
                $img.addClass('loaded fade-in-up');
            });
        }
    });

    // Modern Parallax Effect for Hero Sections
    $(window).on('scroll', function() {
        var scrolled = $(this).scrollTop();
        var parallaxElements = $('.main-slider-item, .page-title-area');

        parallaxElements.each(function() {
            var $this = $(this);
            var speed = 0.5;
            var yPos = -(scrolled * speed);
            $this.css('transform', 'translateY(' + yPos + 'px)');
        });
    });

    // Modern Counter Animation
    function animateCounters() {
        $('.counter').each(function() {
            var $this = $(this);
            var countTo = $this.attr('data-count');

            if (countTo) {
                $({ countNum: $this.text() }).animate({
                    countNum: countTo
                }, {
                    duration: 2000,
                    easing: 'swing',
                    step: function() {
                        $this.text(Math.floor(this.countNum));
                    },
                    complete: function() {
                        $this.text(this.countNum);
                    }
                });
            }
        });
    }

    // Trigger counter animation when in viewport
    $(window).on('scroll', function() {
        $('.counter').each(function() {
            var elementTop = $(this).offset().top;
            var elementBottom = elementTop + $(this).outerHeight();
            var viewportTop = $(window).scrollTop();
            var viewportBottom = viewportTop + $(window).height();

            if (elementBottom > viewportTop && elementTop < viewportBottom) {
                if (!$(this).hasClass('animated')) {
                    $(this).addClass('animated');
                    animateCounters();
                }
            }
        });
    });

    // Modern Search Enhancement
    $('.modern-search-input').on('focus', function() {
        $(this).closest('.search-input-group').addClass('search-focused');
    }).on('blur', function() {
        $(this).closest('.search-input-group').removeClass('search-focused');
    });

    // Add search focus styles
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            .search-focused {
                transform: scale(1.02) !important;
                box-shadow: 0 8px 25px rgba(30, 60, 114, 0.25) !important;
            }

            .search-focused .search-icon {
                color: #1e3c72 !important;
                transform: scale(1.1) !important;
            }
        `)
        .appendTo('head');

    // Modern Mobile Menu Toggle - Updated for new structure
    $(document).on('click', '#mobile-menu-toggle', function() {
        const $toggle = $(this);
        const $overlay = $('#mobile-menu-overlay');
        const $content = $('#mobile-menu-content');

        if ($content.hasClass('active')) {
            // Close menu
            $toggle.removeClass('active');
            $overlay.removeClass('active');
            $content.removeClass('active');
            $('body').css('overflow', '');
        } else {
            // Open menu
            $toggle.addClass('active');
            $overlay.addClass('active');
            $content.addClass('active');
            $('body').css('overflow', 'hidden');
        }
    });

    // Mobile menu close button
    $(document).on('click', '#mobile-menu-close', function() {
        $('#mobile-menu-toggle').removeClass('active');
        $('#mobile-menu-overlay').removeClass('active');
        $('#mobile-menu-content').removeClass('active');
        $('body').css('overflow', '');
    });

    // Mobile menu overlay click
    $(document).on('click', '#mobile-menu-overlay', function() {
        $('#mobile-menu-toggle').removeClass('active');
        $('#mobile-menu-overlay').removeClass('active');
        $('#mobile-menu-content').removeClass('active');
        $('body').css('overflow', '');
    });

    // Mobile categories toggle
    $(document).on('click', '.mobile-categories-header', function() {
        $(this).toggleClass('active');
        $('.mobile-categories-list').toggleClass('active');
    });

    // Close mobile menu when clicking on menu links
    $(document).on('click', '.mobile-nav-link, .mobile-category-item, .mobile-auth-link', function() {
        setTimeout(function() {
            $('#mobile-menu-toggle').removeClass('active');
            $('#mobile-menu-overlay').removeClass('active');
            $('#mobile-menu-content').removeClass('active');
            $('body').css('overflow', '');
        }, 100);
    });

    // Handle window resize
    $(window).on('resize', function() {
        if ($(window).width() > 991) {
            $('#mobile-menu-toggle').removeClass('active');
            $('#mobile-menu-overlay').removeClass('active');
            $('#mobile-menu-content').removeClass('active');
            $('body').css('overflow', '');
        }
    });

    // Add mobile menu styles
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            .mobile-menu-toggle.active span:nth-child(1) {
                transform: rotate(45deg) translate(5px, 5px) !important;
            }

            .mobile-menu-toggle.active span:nth-child(2) {
                opacity: 0 !important;
            }

            .mobile-menu-toggle.active span:nth-child(3) {
                transform: rotate(-45deg) translate(7px, -6px) !important;
            }

            @media (max-width: 991px) {
                .modern-nav-menu {
                    position: fixed !important;
                    top: 0 !important;
                    left: -100% !important;
                    width: 300px !important;
                    height: 100vh !important;
                    background: var(--bg-white) !important;
                    box-shadow: var(--shadow-heavy) !important;
                    transition: var(--transition) !important;
                    z-index: 9999 !important;
                    padding: 80px 30px 30px !important;
                }

                .mobile-menu-open {
                    left: 0 !important;
                }

                .nav-menu-list {
                    flex-direction: column !important;
                    gap: 0 !important;
                }

                .nav-menu-item {
                    width: 100% !important;
                    border-bottom: 1px solid var(--border-light) !important;
                }

                .nav-menu-link {
                    padding: 15px 0 !important;
                    justify-content: flex-start !important;
                }
            }
        `)
        .appendTo('head');

    // Modern Tooltip Enhancement
    $('[data-tooltip]').hover(
        function() {
            var tooltipText = $(this).attr('data-tooltip');
            var $tooltip = $('<div class="modern-tooltip">' + tooltipText + '</div>');
            $('body').append($tooltip);

            var $this = $(this);
            var offset = $this.offset();
            var tooltipWidth = $tooltip.outerWidth();
            var tooltipHeight = $tooltip.outerHeight();

            $tooltip.css({
                top: offset.top - tooltipHeight - 10,
                left: offset.left + ($this.outerWidth() / 2) - (tooltipWidth / 2)
            }).addClass('tooltip-show');
        },
        function() {
            $('.modern-tooltip').remove();
        }
    );

    // Add modern tooltip styles
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            .modern-tooltip {
                position: absolute !important;
                background: var(--text-dark) !important;
                color: var(--text-white) !important;
                padding: 8px 12px !important;
                border-radius: var(--border-radius-small) !important;
                font-size: 12px !important;
                font-weight: 500 !important;
                z-index: 10000 !important;
                opacity: 0 !important;
                transform: translateY(10px) !important;
                transition: var(--transition) !important;
                pointer-events: none !important;
                white-space: nowrap !important;
            }

            .modern-tooltip::after {
                content: '' !important;
                position: absolute !important;
                top: 100% !important;
                left: 50% !important;
                transform: translateX(-50%) !important;
                border: 5px solid transparent !important;
                border-top-color: var(--text-dark) !important;
            }

            .tooltip-show {
                opacity: 1 !important;
                transform: translateY(0) !important;
            }
        `)
        .appendTo('head');

    // Initialize AOS (Animate On Scroll) if available
    if (typeof AOS !== 'undefined') {
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true,
            offset: 100
        });
    }

    // Modern Form Validation Enhancement
    $('input, textarea, select').on('blur', function() {
        var $this = $(this);
        var value = $this.val();

        if (value && value.length > 0) {
            $this.addClass('has-value');
        } else {
            $this.removeClass('has-value');
        }
    });

    // Add form enhancement styles
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            .has-value {
                border-color: #1e3c72 !important;
                background: rgba(30, 60, 114, 0.05) !important;
            }
        `)
        .appendTo('head');

});
