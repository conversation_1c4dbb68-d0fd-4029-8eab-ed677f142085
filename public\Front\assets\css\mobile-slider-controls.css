/* Mobile Slider Controls Hide CSS */
/* This file ensures all slider navigation controls are hidden on mobile devices */

/* Hide Owl Carousel Controls on Tablet and Mobile */
@media (max-width: 1200px) {
    /* Main Home Slider Controls */
    .home-slides.owl-theme .owl-nav,
    .home-slides.owl-theme .owl-dots,
    .home-slides-two.owl-theme .owl-nav,
    .home-slides-two.owl-theme .owl-dots,

    /* Banner Slider Controls */
    .banner_slider .owl-nav,
    .banner_slider .owl-dots,
    #banner_slider .owl-nav,
    #banner_slider .owl-dots,
    #banner_slider2 .owl-nav,
    #banner_slider2 .owl-dots,

    /* Testimonial Slider Controls */
    .testimonial-slides.owl-theme .owl-nav,
    .testimonial-slides.owl-theme .owl-dots,
    #testimonial_slider .owl-nav,
    #testimonial_slider .owl-dots,

    /* Footer Slider Controls */
    #footer_slider .owl-nav,
    #footer_slider .owl-dots,

    /* Generic Owl Carousel Controls */
    .owl-carousel .owl-nav,
    .owl-carousel .owl-dots {
        display: none !important;
        opacity: 0 !important;
        visibility: hidden !important;
        pointer-events: none !important;
    }

    /* Hide Slick Slider Controls */
    .products-details-image-slides .slick-dots,
    .products-details-image-slides .slick-prev,
    .products-details-image-slides .slick-next,
    .slider-for .slick-dots,
    .slider-for .slick-prev,
    .slider-for .slick-next,
    .slider-nav .slick-dots,
    .slider-nav .slick-prev,
    .slider-nav .slick-next,
    .slick-slider .slick-dots,
    .slick-slider .slick-prev,
    .slick-slider .slick-next {
        display: none !important;
        opacity: 0 !important;
        visibility: hidden !important;
        pointer-events: none !important;
    }

    /* Hide any custom navigation buttons */
    .slider-nav-btn,
    .slider-prev,
    .slider-next,
    .carousel-control-prev,
    .carousel-control-next,
    .carousel-indicators {
        display: none !important;
    }
}

/* Extra Small Mobile Devices */
@media (max-width: 767px) {
    /* Force hide all possible slider controls */
    .owl-theme .owl-nav,
    .owl-theme .owl-dots,
    .owl-carousel .owl-nav,
    .owl-carousel .owl-dots,
    .slick-slider .slick-dots,
    .slick-slider .slick-prev,
    .slick-slider .slick-next,
    .carousel-control,
    .carousel-indicators,
    [class*="slider"] .owl-nav,
    [class*="slider"] .owl-dots,
    [class*="banner"] .owl-nav,
    [class*="banner"] .owl-dots {
        display: none !important;
        opacity: 0 !important;
        visibility: hidden !important;
        pointer-events: none !important;
    }
}

/* Ultra Small Mobile Devices */
@media (max-width: 480px) {
    /* Ensure absolutely no slider controls are visible */
    * [class*="owl-nav"],
    * [class*="owl-dots"],
    * [class*="slick-dots"],
    * [class*="slick-prev"],
    * [class*="slick-next"],
    * [class*="carousel-control"],
    * [class*="slider-nav"] {
        display: none !important;
        opacity: 0 !important;
        visibility: hidden !important;
        pointer-events: none !important;
    }

    /* Hide hover effects that might show controls */
    .owl-carousel:hover .owl-nav,
    .owl-carousel:hover .owl-dots,
    .home-slides:hover .owl-nav,
    .banner_slider:hover .owl-nav {
        display: none !important;
        opacity: 0 !important;
        visibility: hidden !important;
    }
}

/* Ensure sliders still work with touch/swipe */
@media (max-width: 991px) {
    .owl-carousel,
    .slick-slider {
        touch-action: pan-y pinch-zoom;
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        -khtml-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
    }

    /* Enable smooth scrolling for touch devices */
    .owl-carousel .owl-stage,
    .slick-slider .slick-track {
        -webkit-overflow-scrolling: touch;
    }
}

/* ========================================
   MOBILE SECTION SPACING OPTIMIZATION
   ======================================== */

/* Reduce section spacing on tablet and mobile devices */
@media (max-width: 1200px) {
    /* Main section padding classes - reduce by 50% */
    .pt-100 { padding-top: 50px !important; }
    .pb-100 { padding-bottom: 50px !important; }
    .ptb-100 {
        padding-top: 50px !important;
        padding-bottom: 50px !important;
    }

    .pt-70 { padding-top: 35px !important; }
    .pb-70 { padding-bottom: 35px !important; }

    .pt-50 { padding-top: 25px !important; }
    .pb-50 { padding-bottom: 25px !important; }

    /* Section areas spacing */
    .overview-area,
    .arrivals-products-area,
    .bestsellers-area,
    .special-products-area,
    .offer-products-area,
    .hot-products-area,
    .blog-area,
    .testimonial-area,
    .partner-area,
    .featured-area,
    .collection-area,
    .support-area,
    .fun-facts-area,
    .team-area,
    .pricing-area,
    .contact-area,
    .faq-area {
        padding-top: 40px !important;
        padding-bottom: 40px !important;
    }

    /* Section titles spacing */
    .section-title {
        margin-bottom: 30px !important;
    }

    /* Row spacing for restaurant view */
    .row_am {
        padding: 40px 0 !important;
    }

    .py_50 {
        padding-top: 25px !important;
        padding-bottom: 25px !important;
    }
}

@media (max-width: 767px) {
    /* Further reduce spacing on smaller mobile devices */
    .pt-100 { padding-top: 30px !important; }
    .pb-100 { padding-bottom: 30px !important; }
    .ptb-100 {
        padding-top: 30px !important;
        padding-bottom: 30px !important;
    }

    .pt-70 { padding-top: 25px !important; }
    .pb-70 { padding-bottom: 25px !important; }

    .pt-50 { padding-top: 20px !important; }
    .pb-50 { padding-bottom: 20px !important; }

    /* Section areas spacing - more compact */
    .overview-area,
    .arrivals-products-area,
    .bestsellers-area,
    .special-products-area,
    .offer-products-area,
    .hot-products-area,
    .blog-area,
    .testimonial-area,
    .partner-area,
    .featured-area,
    .collection-area,
    .support-area,
    .fun-facts-area,
    .team-area,
    .pricing-area,
    .contact-area,
    .faq-area {
        padding-top: 25px !important;
        padding-bottom: 25px !important;
    }

    /* Section titles spacing */
    .section-title {
        margin-bottom: 20px !important;
    }

    /* Row spacing for restaurant view */
    .row_am {
        padding: 25px 0 !important;
    }

    .py_50 {
        padding-top: 20px !important;
        padding-bottom: 20px !important;
    }

    /* Card spacing */
    .single-overview,
    .single-arrivals-products,
    .single-bestsellers-products,
    .single-special-products,
    .single-offer-products,
    .single-hot-products,
    .single-blog,
    .single-partner,
    .single-featured,
    .single-testimonial {
        margin-bottom: 20px !important;
        padding: 20px !important;
    }
}

@media (max-width: 480px) {
    /* Ultra-compact spacing for very small screens */
    .pt-100 { padding-top: 20px !important; }
    .pb-100 { padding-bottom: 20px !important; }
    .ptb-100 {
        padding-top: 20px !important;
        padding-bottom: 20px !important;
    }

    .pt-70 { padding-top: 15px !important; }
    .pb-70 { padding-bottom: 15px !important; }

    .pt-50 { padding-top: 15px !important; }
    .pb-50 { padding-bottom: 15px !important; }

    /* Section areas spacing - ultra compact */
    .overview-area,
    .arrivals-products-area,
    .bestsellers-area,
    .special-products-area,
    .offer-products-area,
    .hot-products-area,
    .blog-area,
    .testimonial-area,
    .partner-area,
    .featured-area,
    .collection-area,
    .support-area,
    .fun-facts-area,
    .team-area,
    .pricing-area,
    .contact-area,
    .faq-area {
        padding-top: 20px !important;
        padding-bottom: 20px !important;
    }

    /* Section titles spacing */
    .section-title {
        margin-bottom: 15px !important;
    }

    /* Row spacing for restaurant view */
    .row_am {
        padding: 20px 0 !important;
    }

    .py_50 {
        padding-top: 15px !important;
        padding-bottom: 15px !important;
    }

    /* Card spacing - ultra compact */
    .single-overview,
    .single-arrivals-products,
    .single-bestsellers-products,
    .single-special-products,
    .single-offer-products,
    .single-hot-products,
    .single-blog,
    .single-partner,
    .single-featured,
    .single-testimonial {
        margin-bottom: 15px !important;
        padding: 15px !important;
    }

    /* Container padding */
    .container {
        padding-left: 10px !important;
        padding-right: 10px !important;
    }

    /* Additional spacing optimizations */
    .main-slider-item {
        height: auto !important;
        min-height: 400px !important;
    }

    /* Footer spacing */
    .footer-area {
        padding-top: 30px !important;
        padding-bottom: 20px !important;
    }

    /* Breadcrumb spacing */
    .page-title-area {
        padding-top: 80px !important;
        padding-bottom: 80px !important;
    }
}
