/* Mobile Slider Controls Hide CSS */
/* This file ensures all slider navigation controls are hidden on mobile devices */

/* Hide Owl Carousel Controls on Mobile */
@media (max-width: 991px) {
    /* Main Home Slider Controls */
    .home-slides.owl-theme .owl-nav,
    .home-slides.owl-theme .owl-dots,
    .home-slides-two.owl-theme .owl-nav,
    .home-slides-two.owl-theme .owl-dots,
    
    /* Banner Slider Controls */
    .banner_slider .owl-nav,
    .banner_slider .owl-dots,
    #banner_slider .owl-nav,
    #banner_slider .owl-dots,
    #banner_slider2 .owl-nav,
    #banner_slider2 .owl-dots,
    
    /* Testimonial Slider Controls */
    .testimonial-slides.owl-theme .owl-nav,
    .testimonial-slides.owl-theme .owl-dots,
    #testimonial_slider .owl-nav,
    #testimonial_slider .owl-dots,
    
    /* Footer Slider Controls */
    #footer_slider .owl-nav,
    #footer_slider .owl-dots,
    
    /* Generic Owl Carousel Controls */
    .owl-carousel .owl-nav,
    .owl-carousel .owl-dots {
        display: none !important;
        opacity: 0 !important;
        visibility: hidden !important;
        pointer-events: none !important;
    }
    
    /* Hide Slick Slider Controls */
    .products-details-image-slides .slick-dots,
    .products-details-image-slides .slick-prev,
    .products-details-image-slides .slick-next,
    .slider-for .slick-dots,
    .slider-for .slick-prev,
    .slider-for .slick-next,
    .slider-nav .slick-dots,
    .slider-nav .slick-prev,
    .slider-nav .slick-next,
    .slick-slider .slick-dots,
    .slick-slider .slick-prev,
    .slick-slider .slick-next {
        display: none !important;
        opacity: 0 !important;
        visibility: hidden !important;
        pointer-events: none !important;
    }
    
    /* Hide any custom navigation buttons */
    .slider-nav-btn,
    .slider-prev,
    .slider-next,
    .carousel-control-prev,
    .carousel-control-next,
    .carousel-indicators {
        display: none !important;
    }
}

/* Extra Small Mobile Devices */
@media (max-width: 767px) {
    /* Force hide all possible slider controls */
    .owl-theme .owl-nav,
    .owl-theme .owl-dots,
    .owl-carousel .owl-nav,
    .owl-carousel .owl-dots,
    .slick-slider .slick-dots,
    .slick-slider .slick-prev,
    .slick-slider .slick-next,
    .carousel-control,
    .carousel-indicators,
    [class*="slider"] .owl-nav,
    [class*="slider"] .owl-dots,
    [class*="banner"] .owl-nav,
    [class*="banner"] .owl-dots {
        display: none !important;
        opacity: 0 !important;
        visibility: hidden !important;
        pointer-events: none !important;
    }
}

/* Ultra Small Mobile Devices */
@media (max-width: 480px) {
    /* Ensure absolutely no slider controls are visible */
    * [class*="owl-nav"],
    * [class*="owl-dots"],
    * [class*="slick-dots"],
    * [class*="slick-prev"],
    * [class*="slick-next"],
    * [class*="carousel-control"],
    * [class*="slider-nav"] {
        display: none !important;
        opacity: 0 !important;
        visibility: hidden !important;
        pointer-events: none !important;
    }
    
    /* Hide hover effects that might show controls */
    .owl-carousel:hover .owl-nav,
    .owl-carousel:hover .owl-dots,
    .home-slides:hover .owl-nav,
    .banner_slider:hover .owl-nav {
        display: none !important;
        opacity: 0 !important;
        visibility: hidden !important;
    }
}

/* Ensure sliders still work with touch/swipe */
@media (max-width: 991px) {
    .owl-carousel,
    .slick-slider {
        touch-action: pan-y pinch-zoom;
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        -khtml-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
    }
    
    /* Enable smooth scrolling for touch devices */
    .owl-carousel .owl-stage,
    .slick-slider .slick-track {
        -webkit-overflow-scrolling: touch;
    }
}
