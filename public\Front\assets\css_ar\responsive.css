@media only screen and (max-width: 767px) {
  .pb-70 {
    padding-bottom: 20px;
  }
  .pt-100 {
    padding-top: 50px;
  }
  .pb-100 {
    padding-bottom: 50px;
  }
  .ptb-100 {
    padding-top: 50px;
    padding-bottom: 50px;
  }
  .section-title h2 {
    font-size: 25px;
  }
  .top-header-content {
    text-align: center;
  }
  .top-header-optional {
    text-align: center;
    margin-top: 5px;
  }
  .middle-header-logo a img {
    display: none;
  }
  .middle-header-search form .form-group .nice-select {
    line-height: 50px;
    height: 50px;
  }
  .middle-header-optional {
    margin-top: 20px;
    text-align: center;
  }
  .mean-container a.meanmenu-reveal {
    padding: 5px 0 0 0;
  }
  .mean-container a.meanmenu-reveal span {
    display: block;
    background: #000;
    height: 4px;
    margin-top: -5px;
    border-radius: 3px;
    position: relative;
    top: 8px;
  }
  .mean-container .mean-bar {
    background-color: unset;
    border-bottom: none;
  }
  .mean-container .mean-nav {
    margin-top: 40px;
  }
  .main-responsive-nav .logo img {
    position: relative;
    max-width: 60px !important;
  }
  .others-option-for-responsive .dot-menu {
    top: -30px;
  }
  .others-option-for-responsive .container .container {
    right: 20px;
    max-width: 240px;
    padding-top: 10px;
    padding-bottom: 10px;
    border-radius: 5px;
    text-align: center;
  }
  .others-option-for-responsive .option-inner .others-options {
    margin-left: 0;
  }
  .others-option-for-responsive .option-inner .others-options .option-item {
    margin-bottom: 10px;
    margin-right: 0;
  }
  .others-option-for-responsive .option-inner .others-options .option-item:last-child {
    margin-bottom: 0;
  }
  .others-option-for-responsive .option-inner .others-options .option-item .language-switcher .dropdown-menu {
    margin-top: 10px;
  }
  .others-option-for-responsive .option-inner .others-options.d-flex {
    display: block !important;
  }
  .main-slider-content h1 {
    font-size: 30px;
  }
  .main-slider-content p {
    font-size: 15px;
  }
  .main-slider-image {
    margin-top: 30px;
    text-align: center;
  }
  .main-slider-image img {
    max-width: 350px;
    margin: auto;
  }
  .home-slides.owl-theme .owl-nav .owl-prev, .home-slides.owl-theme .owl-nav .owl-next {
    top: 95%;
    -webkit-transform: translateY(-100%);
            transform: translateY(-100%);
  }
  .single-overview .overview-image {
    margin-bottom: 30px;
    text-align: center;
  }
  .single-overview .overview-content h3 {
    font-size: 20px;
  }
  .single-overview-item .overview-content h3 {
    font-size: 20px;
  }
  .single-overview-item .overview-image {
    margin-top: 20px;
    text-align: center;
  }
  .support-inner-box {
    padding: 30px 50px 10px;
  }
  .single-support {
    text-align: center;
    margin-bottom: 20px;
  }
  .single-support .icon {
    position: relative;
    left: 0;
    top: 0;
    margin-bottom: 10px;
  }
  .single-support .support-content {
    padding-left: 0;
    padding-right: 0;
  }
  .single-support::before {
    display: none;
  }
  .single-arrivals-products .arrivals-products-content h3 {
    font-size: 20px;
  }
  .single-arrivals-products .arrivals-products-content span {
    font-size: 16px;
  }
  .single-arrivals-products .arrivals-products-image img {
    width: 100%;
  }
  .single-offer-products .offer-products-content h3 {
    font-size: 20px;
  }
  .single-offer-products .offer-products-image img {
    width: 100%;
  }
  .single-offer-products-box .offer-products-image img {
    width: 100%;
  }
  .collection-inner-box {
    padding: 20px 15px;
  }
  .collection-inner-box .collection-content {
    margin-top: 20px;
    margin-bottom: 20px;
  }
  .collection-inner-box .collection-content h3 {
    font-size: 20px;
  }
  .collection-inner-box .collection-content span {
    font-size: 16px;
  }
  .collection-inner-box .collection-content p {
    font-size: 15px;
  }
  .collection-inner-box .collection-content .collection-btn {
    margin-top: 20px;
  }
  .bestsellers-list-tab .tabs li {
    margin-right: 10px;
    margin-left: 10px;
    margin-bottom: 10px;
  }
  .single-bestsellers-products .bestsellers-products-content h3 {
    font-size: 20px;
  }
  .single-bestsellers-products .bestsellers-products-content span {
    font-size: 15px;
  }
  .single-bestsellers-products .bestsellers-products-image img {
    width: 100%;
  }
  .special-products-inner {
    height: 530px;
    margin-bottom: 30px;
  }
  .special-products-title h2 {
    font-size: 25px;
  }
  .single-blog .blog-content h3 {
    font-size: 20px;
  }
  .single-footer-widget h2 {
    font-size: 25px;
  }
  .single-special-products .special-products-content h3 {
    font-size: 20px;
  }
  .single-special-products .special-products-content span {
    font-size: 15px;
  }
  .single-special-products .special-products-image img {
    width: 100%;
  }
  .single-hot-products .hot-products-image img {
    width: 100%;
  }
  .productsQuickView .modal-content {
    padding: 35px 15px;
  }
  .productsQuickView .modal-content .product-content {
    margin-top: 25px;
  }
  .productsQuickView .modal-content .product-content h3 {
    font-size: 20px;
  }
  .productsQuickView .modal-content .product-content .product-add-to-cart .default-btn {
    padding: 12px 20px;
  }
  .productsQuickView .modal-content .product-content .product-review .rating {
    font-size: 20px;
  }
  .productsQuickView .modal-content .product-content .price {
    font-size: 18px;
  }
  .main-slider-item-box {
    padding-top: 30px;
    padding-bottom: 60px;
    padding-left: 15px;
    padding-right: 15px;
    position: relative;
    z-index: 1;
  }
  .main-slider-item-box::before {
    position: absolute;
    content: "";
    height: 100%;
    width: 100%;
    background-color: #000000;
    left: 0;
    right: 0;
    top: 0;
    z-index: -1;
    opacity: .50;
  }
  .main-slider-item-box .main-slider-content b {
    color: #ffffff;
  }
  .main-slider-item-box .main-slider-content h1 {
    font-size: 25px;
    color: #ffffff;
  }
  .main-slider-item-box .main-slider-content p {
    font-size: 15px;
    color: #ffffff;
  }
  .single-featured .featured-content {
    padding: 15px;
  }
  .single-featured .featured-content h3 {
    font-size: 18px;
  }
  .offer-overview {
    padding: 25px 25px;
    text-align: center;
    margin-bottom: 30px;
  }
  .offer-overview .offer-image {
    margin-bottom: 20px;
  }
  .testimonial-item {
    padding: 25px 15px 25px;
  }
  .testimonial-item .content p {
    font-size: 15px;
  }
  .testimonial-item .content .name h3 {
    font-size: 20px;
  }
  .testimonial-slides.owl-theme .owl-nav [class*=owl-] {
    display: none;
  }
  .main-slider-item-others {
    padding-top: 30px;
    padding-bottom: 60px;
    padding-left: 15px;
    padding-right: 15px;
    position: relative;
    z-index: 1;
  }
  .main-slider-item-others::before {
    position: absolute;
    content: "";
    height: 100%;
    width: 100%;
    background-color: #000000;
    left: 0;
    right: 0;
    top: 0;
    z-index: -1;
    opacity: .50;
  }
  .main-slider-item-others .main-slider-content b {
    color: #ffffff;
  }
  .main-slider-item-others .main-slider-content h1 {
    font-size: 25px;
    color: #ffffff;
  }
  .main-slider-item-others .main-slider-content p {
    font-size: 15px;
    color: #ffffff;
  }
  .single-hot-featured .featured-content {
    padding: 15px;
  }
  .single-hot-featured .featured-content h3 {
    font-size: 18px;
  }
  .single-hot-featured .hot-products-image img {
    width: 100%;
  }
  .main-slider-area .main-slider-item-box {
    padding-top: 35px;
    padding-bottom: 100px;
    padding-left: 15px;
    padding-right: 15px;
  }
  .page-title-content {
    text-align: center;
  }
  .page-title-content h2 {
    font-size: 22px;
    margin-bottom: 15px;
  }
  .page-title-content ul {
    text-align: center;
    position: relative;
    top: unset;
    -webkit-transform: unset;
            transform: unset;
  }
  .story-title h2 {
    font-size: 20px;
  }
  .story-image {
    height: 450px;
  }
  .story-content {
    padding: 30px 0 0;
  }
  .story-content h3 {
    font-size: 25px;
  }
  .mission-content {
    padding: 0 0 30px;
  }
  .mission-content h3 {
    font-size: 25px;
  }
  .mission-image {
    height: 450px;
  }
  .vision-content {
    padding: 30px 0 0;
  }
  .vision-content h3 {
    font-size: 25px;
  }
  .vision-image {
    height: 450px;
  }
  .single-fun-fact::before {
    display: none;
  }
  .coming-soon-content {
    padding: 30px 25px;
  }
  .coming-soon-content #timer {
    margin-top: 30px;
  }
  .coming-soon-content #timer div {
    width: 100px;
    height: 105px;
    margin-left: 5px;
    margin-right: 5px;
    font-size: 20px;
    margin-top: 10px;
  }
  .coming-soon-content h2 {
    font-size: 25px;
  }
  .track-order-image {
    text-align: center;
    margin-bottom: 30px;
  }
  .track-order-content h2 {
    font-size: 20px;
    margin-bottom: 10px;
  }
  .products-filter-options p {
    margin-bottom: 25px;
  }
  .pagination-area {
    margin-bottom: 45px;
    margin-top: 20px;
  }
  .cart-table table thead tr th {
    padding: 0 35px 15px;
  }
  .single-shop-products .shop-products-image img {
    width: 100%;
  }
  .cart-buttons .shopping-coupon-code {
    margin-bottom: 30px;
  }
  .text-right {
    text-align: center !important;
  }
  .cart-totals {
    padding: 15px;
    margin-top: 30px;
  }
  .order-details {
    margin-top: 30px;
  }
  .products-details-desc .product-content {
    margin-top: 25px;
  }
  .products-details-desc .product-content h3 {
    font-size: 20px;
  }
  .products-details-desc .product-content .product-add-to-cart .default-btn {
    padding: 12px 20px;
  }
  .products-details-desc .product-content .product-review .rating {
    font-size: 20px;
  }
  .products-details-desc .product-content .price {
    font-size: 18px;
  }
  .blog-details-desc {
    margin-bottom: 30px;
  }
  .blog-details-desc .article-content .details-content h3 {
    font-size: 25px;
  }
  .blog-details-desc .article-footer .article-tags {
    -webkit-box-flex: unset;
        -ms-flex: unset;
            flex: unset;
    max-width: unset;
  }
  .blog-details-desc .article-footer .article-share {
    -webkit-box-flex: unset;
        -ms-flex: unset;
            flex: unset;
    max-width: unset;
    margin-top: 20px;
  }
  .modal-newsletter-area .modal-newsletter-wrap {
    padding: 35px 15px;
  }
  .modal-newsletter-area .modal-newsletter-image {
    height: 250px;
  }
  .collection-inner-box::before {
    display: none;
  }

  .login-form {
    padding: 40px 20px;
  }
  .login-form .lost-your-password {
    text-align: left !important;
    margin-top: 15px;
  }
  .register-form {
    padding: 40px 20px;
  }
  .products-details-tabs .nav .nav-item {
    margin-right: 10px;
  }
  .products-details-tabs .nav .nav-item .nav-link {
    font-size: 14px;
  }
  .blog-area.bg-ffffff .pagination-area {
    margin-bottom: 0;
    margin-top: 0;
  }
  .shop-area.bg-ffffff .pagination-area {
    margin-bottom: 0;
    margin-top: 0;
  }
  br {
    display: none;
  }
  .offer-soon-content #timer div {
    width: 52px;
    font-size: 15px;
  }
  .offer-soon-content #timer div span {
    font-size: 14px;
  }
  .products-details-area.bg-color .widget-area {
    margin-top: 30px;
  }
  .contact-information {
    margin-top: 30px;
  }
}

@media only screen and (min-width: 576px) and (max-width: 767px) {
  .login-form .lost-your-password {
    text-align: right !important;
    margin-top: 0;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .pb-70 {
    padding-bottom: 20px;
  }
  .pt-100 {
    padding-top: 50px;
  }
  .pb-100 {
    padding-bottom: 50px;
  }
  .ptb-100 {
    padding-top: 50px;
    padding-bottom: 50px;
  }
  .section-title h2 {
    font-size: 25px;
  }
  .top-header-content {
    text-align: center;
  }
  .top-header-optional {
    text-align: center;
    margin-top: 5px;
  }
  .middle-header-logo a img {
    display: none;
  }
  .middle-header-search {
    padding: 0 20px 0 20px;
  }
  .middle-header-search form .form-group .nice-select {
    line-height: 50px;
    height: 50px;
  }
  .middle-header-optional {
    margin-top: 20px;
    text-align: center;
  }
  .mean-container a.meanmenu-reveal {
    padding: 5px 0 0 0;
  }
  .mean-container a.meanmenu-reveal span {
    display: block;
    background: #000;
    height: 4px;
    margin-top: -5px;
    border-radius: 3px;
    position: relative;
    top: 8px;
  }
  .mean-container .mean-bar {
    background-color: unset;
    border-bottom: none;
  }
  .mean-container .mean-nav {
    margin-top: 40px;
  }
  .main-responsive-nav .logo img {
    position: relative;
    max-width: 60px !important;
  }
  .others-option-for-responsive .dot-menu {
    top: -30px;
  }
  .others-option-for-responsive .container .container {
    right: 20px;
    max-width: 240px;
    padding-top: 10px;
    padding-bottom: 10px;
    border-radius: 5px;
    text-align: center;
  }
  .others-option-for-responsive .option-inner .others-options {
    margin-left: 0;
  }
  .others-option-for-responsive .option-inner .others-options .option-item {
    margin-bottom: 10px;
    margin-right: 0;
  }
  .others-option-for-responsive .option-inner .others-options .option-item:last-child {
    margin-bottom: 0;
  }
  .others-option-for-responsive .option-inner .others-options .option-item .language-switcher .dropdown-menu {
    margin-top: 10px;
  }
  .others-option-for-responsive .option-inner .others-options.d-flex {
    display: block !important;
  }
  .main-slider-content h1 {
    font-size: 35px;
  }
  .main-slider-content p {
    font-size: 15px;
  }
  .main-slider-image {
    margin-top: 30px;
    text-align: center;
  }
  .main-slider-image img {
    max-width: 350px;
    margin: auto;
  }
  .home-slides.owl-theme .owl-nav .owl-prev, .home-slides.owl-theme .owl-nav .owl-next {
    top: 95%;
    -webkit-transform: translateY(-100%);
            transform: translateY(-100%);
  }
  .single-overview .overview-image {
    margin-bottom: 30px;
    text-align: center;
  }
  .single-overview .overview-content h3 {
    font-size: 20px;
  }
  .single-overview-item .overview-content h3 {
    font-size: 20px;
  }
  .single-overview-item .overview-image {
    margin-top: 20px;
    text-align: center;
  }
  .overview-area {
    padding-bottom: 50px !important;
  }
  .support-inner-box {
    padding: 30px 50px 10px;
  }
  .single-support {
    text-align: center;
    margin-bottom: 20px;
  }
  .single-support .icon {
    position: relative;
    left: 0;
    top: 0;
    margin-bottom: 10px;
  }
  .single-support .support-content {
    padding-left: 0;
    padding-right: 0;
  }
  .single-support::before {
    display: none;
  }
  .single-arrivals-products .arrivals-products-content h3 {
    font-size: 20px;
  }
  .single-arrivals-products .arrivals-products-content span {
    font-size: 16px;
  }
  .single-offer-products .offer-products-content h3 {
    font-size: 20px;
  }
  .collection-inner-box {
    padding: 20px 15px;
  }
  .collection-inner-box .collection-content {
    margin-top: 20px;
    margin-bottom: 20px;
  }
  .collection-inner-box .collection-content h3 {
    font-size: 25px;
  }
  .collection-inner-box .collection-content span {
    font-size: 20px;
  }
  .collection-inner-box .collection-content p {
    font-size: 15px;
  }
  .collection-inner-box .collection-content .collection-btn {
    margin-top: 20px;
  }
  .bestsellers-list-tab .tabs li {
    margin-right: 10px;
    margin-left: 10px;
    margin-bottom: 10px;
  }
  .single-bestsellers-products .bestsellers-products-content h3 {
    font-size: 20px;
  }
  .single-bestsellers-products .bestsellers-products-content span {
    font-size: 15px;
  }
  .special-products-inner {
    height: 630px;
    margin-bottom: 30px;
  }
  .special-products-title h2 {
    font-size: 25px;
  }
  .single-blog .blog-content h3 {
    font-size: 20px;
  }
  .single-footer-widget h2 {
    font-size: 25px;
  }
  .single-special-products .special-products-content h3 {
    font-size: 20px;
  }
  .single-special-products .special-products-content span {
    font-size: 15px;
  }
  .productsQuickView .modal-content {
    padding: 35px 15px;
  }
  .productsQuickView .modal-content .product-content {
    margin-top: 25px;
  }
  .productsQuickView .modal-content .product-content h3 {
    font-size: 20px;
  }
  .productsQuickView .modal-content .product-content .product-add-to-cart .default-btn {
    padding: 12px 20px;
  }
  .productsQuickView .modal-content .product-content .product-review .rating {
    font-size: 20px;
  }
  .productsQuickView .modal-content .product-content .price {
    font-size: 18px;
  }
  .single-featured .featured-content {
    padding: 15px;
  }
  .single-featured .featured-content h3 {
    font-size: 18px;
  }
  .offer-overview {
    padding: 25px 25px;
    text-align: center;
    margin-bottom: 30px;
  }
  .offer-overview .offer-image {
    margin-bottom: 20px;
  }
  .testimonial-item {
    padding: 25px 15px 25px;
  }
  .testimonial-item .content p {
    font-size: 15px;
  }
  .testimonial-item .content .name h3 {
    font-size: 20px;
  }
  .testimonial-slides.owl-theme .owl-nav [class*=owl-] {
    display: none;
  }
  .single-featured img {
    width: 100%;
  }
  .single-hot-featured img {
    width: 100%;
  }
  .single-fun-fact::before {
    display: none;
  }
  .coming-soon-content {
    padding: 30px 25px;
  }
  .coming-soon-content #timer {
    margin-top: 30px;
  }
  .coming-soon-content #timer div {
    width: 100px;
    height: 105px;
    margin-left: 5px;
    margin-right: 5px;
    font-size: 20px;
    margin-top: 10px;
  }
  .coming-soon-content h2 {
    font-size: 25px;
  }
  .track-order-image {
    text-align: center;
    margin-bottom: 30px;
  }
  .track-order-content h2 {
    font-size: 20px;
    margin-bottom: 10px;
  }
  .pagination-area {
    margin-bottom: 45px;
    margin-top: 20px;
  }
  .cart-totals {
    padding: 15px;
    margin-top: 30px;
  }
  .order-details {
    margin-top: 30px;
  }
  .blog-details-desc {
    margin-bottom: 30px;
  }
  .blog-details-desc .article-content .details-content h3 {
    font-size: 25px;
  }
  .collection-inner-box::before {
    display: none;
  }
  .main-slider-item-box {
    position: relative;
    z-index: 1;
  }
  .main-slider-item-box::before {
    position: absolute;
    content: "";
    height: 100%;
    width: 100%;
    background-color: #000000;
    left: 0;
    right: 0;
    top: 0;
    z-index: -1;
    opacity: .50;
  }
  .main-slider-item-box .main-slider-content b {
    color: #ffffff;
  }
  .main-slider-item-box .main-slider-content h1 {
    color: #ffffff;
  }
  .main-slider-item-box .main-slider-content p {
    color: #ffffff;
  }
  .main-slider-item-others {
    position: relative;
    z-index: 1;
  }
  .main-slider-item-others::before {
    position: absolute;
    content: "";
    height: 100%;
    width: 100%;
    background-color: #000000;
    left: 0;
    right: 0;
    top: 0;
    z-index: -1;
    opacity: .50;
  }
  .main-slider-item-others .main-slider-content b {
    color: #ffffff;
  }
  .main-slider-item-others .main-slider-content h1 {
    color: #ffffff;
  }
  .main-slider-item-others .main-slider-content p {
    color: #ffffff;
  }
  .blog-area.bg-ffffff .pagination-area {
    margin-bottom: 0;
    margin-top: 0;
  }
  .products-details-area.bg-color .widget-area {
    margin-top: 30px;
  }
  .contact-information {
    margin-top: 30px;
  }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .main-navbar .navbar .navbar-nav .nav-item .dropdown-menu li .dropdown-menu {
    left: -300px;
    right: auto;
  }
  .single-overview-item {
    padding: 45px 25px;
  }
  .single-support .icon {
    top: -5px;
  }
  .single-arrivals-products .arrivals-products-content h3 {
    font-size: 20px;
  }
  .single-arrivals-products .arrivals-products-content span {
    font-size: 15px;
  }
  .single-offer-products .offer-products-content h3 {
    font-size: 22px;
  }
  .single-bestsellers-products .bestsellers-products-content h3 {
    font-size: 20px;
  }
  .single-special-products .special-products-content h3 {
    font-size: 22px;
  }
  .single-blog .blog-content h3 {
    font-size: 20px;
  }
  .single-footer-widget .footer-social li i {
    height: 40px;
    width: 40px;
    line-height: 40px;
  }
  .slider-categories li a {
    padding: 14px 12px;
    font-size: 14px;
  }
  .single-featured .featured-content {
    padding: 15px;
  }
  .single-featured .featured-content h3 {
    font-size: 18px;
  }
  .offer-overview {
    margin-bottom: 30px;
  }
  .collection-inner-box::before {
    left: -105px;
  }
  .main-slider-item-box {
    position: relative;
    z-index: 1;
  }
  .main-slider-item-box::before {
    position: absolute;
    content: "";
    height: 100%;
    width: 100%;
    background-color: #000000;
    left: 0;
    right: 0;
    top: 0;
    z-index: -1;
    opacity: .50;
  }
  .main-slider-item-box .main-slider-content b {
    color: #ffffff;
  }
  .main-slider-item-box .main-slider-content h1 {
    color: #ffffff;
  }
  .main-slider-item-box .main-slider-content p {
    color: #ffffff;
  }
  .main-slider-item-others {
    position: relative;
    z-index: 1;
  }
  .main-slider-item-others::before {
    position: absolute;
    content: "";
    height: 100%;
    width: 100%;
    background-color: #000000;
    left: 0;
    right: 0;
    top: 0;
    z-index: -1;
    opacity: .50;
  }
  .main-slider-item-others .main-slider-content b {
    color: #ffffff;
  }
  .main-slider-item-others .main-slider-content h1 {
    color: #ffffff;
  }
  .main-slider-item-others .main-slider-content p {
    color: #ffffff;
  }
  .offer-soon-content #timer div {
    width: 52px;
    font-size: 15px;
  }
  .offer-soon-content #timer div span {
    font-size: 14px;
  }
}
/*# sourceMappingURL=responsive.css.map */
